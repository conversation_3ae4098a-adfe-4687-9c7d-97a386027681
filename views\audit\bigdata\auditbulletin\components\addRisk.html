<!--#include virtual ="include/header.html"-->
<style>

    .layui-table tr td .layui-table-cell {
         height: auto;
        overflow: auto;
        white-space: break-spaces;
    }
    .layui-table-body{
        overflow-x: hidden;
    }
</style>
<body>
<div class="layui-fluid larry-wrapper" style="padding-bottom: 60px;">
    <section class="panel panel-padding">
        <form class="layui-form layui-form-pane form-conmon form-conmon-more"
              data-params='{"dataName":"prjSendInfoSearch", "action":"list", "ajax":false, "bind":false}'
              id="prjSendInfoSearch">
            <div class="transition-500ms search-condition">
                <div class="layui-form-item layui-form-item-sm">
                    <div class="layui-row layui-col-space10 ">
                        <div class="layui-col-xs4">
                            <label class="layui-form-label">年度</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="remoteYear" name="remoteYear" readonly placeholder="年度"
                                       type="text">
                            </div>
                        </div>
                        <div class="layui-col-xs4">
                            <label class="layui-form-label">来源</label>
                            <div class="layui-input-block">
                                <select id="spanTypeName" lay-filter="spanTypeName" lay-search=""
                                        name="spanTypeName"></select>
                            </div>
                        </div>
                        <div class="layui-col-xs4">
                            <label class="layui-form-label">专题</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="topicName" name="topicName" placeholder=""
                                       type="text">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-form-item-sm">
                    <div class="layui-row layui-col-space12 ">
                        <div class="layui-col-xs4">
                            <label class="layui-form-label">子专题</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="subTopicName" name="subTopicName" placeholder=""
                                       type="text">
                            </div>
                        </div>
                        <div class="layui-col-xs4">
                            <label class="layui-form-label">负责人/分析人</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="principalName" name="principalName" type="text">
                            </div>
                        </div>
                        <div class="layui-col-xs4">
                            <label class="layui-form-label">团队</label>
                            <div class="layui-input-block">
                                <select id="groupId" lay-filter="groupId"  required="required" name="groupId"
                                        type="text"></select>
                            </div>
                        </div>
                        <div class="layui-col-md12 layui-col-sm12 refer-content">
                            <a class="btnMore" href="javascript:">
                                <span>更多搜索</span>
                                <i class="iconfont slide-down">&#xe603;</i>
                                <i class="iconfont slide-up noneJaudit">&#xe605;</i>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-form-item-sm">

                    <div class="layui-row layui-col-space12  search-more noneJaudit">

                        <div class="layui-col-xs4">
                            <label class="layui-form-label">聚焦风险</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="initialFocusRisk" name="initialFocusRisk"
                                       type="text">
                            </div>
                        </div>

                        <div class="layui-col-xs4">
                            <label class="layui-form-label">风研编号</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="achievementCode" name="achievementCode"
                                       type="text">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="search-btn">
                <div class="layui-form-item layui-form-item-sm">
                    <a class="layui-btn btn-outline layui-btn-normal layui-btn-sm" id="searchBtn">
                        <i class="iconfont search-icon">&#xe60b;</i> 查询
                    </a>
                </div>
                <div class="layui-form-item layui-form-item-sm">
                    <button class="layui-btn btn-outline layui-btn-normal layui-btn-sm" id="resetBtn">
                        <i class="iconfont search-icon">&#xe63a;</i> 重置
                    </button>
                </div>
            </div>
        </form>
        <div class="layui-form">
            <table class="layui-table" id="infoTable" lay-filter="infoTable"></table>
        </div>
        <div class="layui-common-box layui-row layui-col-md12 layui-col-sm12 layui-col-lg12 fixedBottom layui-form">
            <div style="text-align: right;padding-top: 5px;">
                <div class="layui-inline">
                    <button class="layui-btn btn-outline layui-btn-normal layui-btn-sm"
                            onclick="saveTask();"
                            type="button">
                        <i class="iconfont search-icon">&#xe6a7;</i> 保存
                    </button>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-sm layui-btn-primary btn-radius" id="reset" onclick="closePage()"
                            type="button">
                        <i class="iconfont search-icon">&#xe687;</i> 关闭
                    </button>
                </div>
            </div>
        </div>
    </section>
</div>
</body>
<!--#include virtual ="include/version.html"-->
<!--来源-->
<script id="select-dic-tpl" type="text/html">
    <option selected value="">--请选择--</option>
    {{# layui.each(d, function(index, item){ }}
    <option value="{{item.code}}">{{item.codeText}}</option>
    {{# }); }}
</script>
<!--团队-->
<script id="select-group-tpl" type="text/html">
    <option selected value="">--请选择--</option>
    {{# layui.each(d, function(index, item){ }}
    <option value="{{item.ID}}">{{item.GROUP_NAME}}</option>
    {{# }); }}
</script>
<script type="text/javascript">
    var  id = getUrlParam("id");
    var  project_id = getUrlParam("project_id");
    layui.use(['jqform', 'table', 'laytpl', 'jquery', 'laydate', 'layer', 'jqbind','jqfrm'], function () {
        var $ = layui.jquery,
            tpl = layui.laytpl,
            jqbind = layui.jqbind,
            frm = layui.jqfrm,
            jqajax = layui.jqajax,
            layer = layui.layer,
            form = layui.jqform,
            laydate = layui.laydate,
            ctx = top.global.ctx,
            json = layui.json,
            table = layui.table;

var typeDictList = [];
        // BULLETIN_STATUS 加載
        function initTaskStatus(type, data) {
            typeDictList = data;
            var getTpl = $('#select-dic-tpl').html();
            tpl(getTpl).render(data, function (html) {
                $("#spanTypeName").html(html);
            });
            form.render('select', 'spanTypeName');
        }

        function selectGroupData() {
            $.ajax({
                url: ctx + "/bdata/remoteDataLeaderHome/selectGroupData",
                dataType: "json",
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    var getTpl = $("#select-group-tpl").html();
                    tpl(getTpl).render(ret.data, function (html) {
                        $("#groupId").html(html);
                    });
                    $("#groupId").val('');
                    form.render('select', 'groupId');
                },
                error: function (e) {
                    console.info(e);
                }
            });
        }

        selectGroupData();

        //获取任务类型字典
        getDicList('DATA_BOARD_SPAN_TYPE', initTaskStatus);


        function timeLimitData() {
            var data = conditionShow();
            var url = ctx + "/pro/auditbulletin/queryRiskListByBulletinId";
            if(project_id){
                url = ctx + "/pro/auditbulletin/queryRiskListByProjectId";
            }
            table.render({
                elem: '#infoTable',
                id:'infoTable',
                height:'full-200',
                url: url,
                where: data,
                page: true,
                even: true,
                cols: [
                    [
                        {type: 'checkbox', width: '5%'},
                        {type: 'numbers', title: '序号', width: '5%'},
                        {
                            field: 'achievementCode',
                            title: '风研编号',
                            width: '8%',
                            align: 'center'
                        },
                        {
                            field: 'remoteYear',
                            title: '年度',
                            width: '5%',
                            align: 'center'
                        },
                        {
                            field: 'spanType',
                            title: '来源',
                            width: '6%',
                            align: 'center',
                            style: 'text-align: center;',
                            templet: function (d) {
                                return fromatComon(d.spanType,typeDictList);
                            }
                        },
                        {
                            field: 'topicName',
                            title: '专题',
                            width: '8%',
                            align: 'center',
                            style: 'text-align: left;'
                        },
                        {
                            field: 'subTopicName',
                            title: '子专题',
                            width: '8%',
                            align: 'center',
                            style: 'text-align: left;'
                        },

                        {
                            field: 'initialFocusRisk',
                            title: '聚焦风险',
                            width: '31%',
                            align: 'center',
                            style: 'text-align: left;'
                        },
                        {
                            field: 'chargePersonName',
                            title: '负责人',
                            width: '8%',
                            align: 'center',
                            style: 'text-align: center;'
                        },
                        {
                            field: 'analystPersonName',
                            title: '分析人',
                            width: '8%',
                            align: 'center',
                            style: 'text-align: left;'
                        },
                        {
                            field: 'groupName',
                            title: '团队',
                            width: '8%',
                            align: 'center',
                            style: 'text-align: left;'
                        }
                    ]
                ],
                done: function (res) {

                }
            });
        }
        timeLimitData()
        function conditionShow() {
            // 年度
            var remoteYear =$('#remoteYear').val();
            // 来源
            var spanType = $('#spanTypeName').val();
            // 专题
            var topicName = $('#topicName').val();
            // 子专题
            var subTopicName = $('#subTopicName').val();
            // 负责人/分析人
            var principalName = $('#principalName').val();
            // 团队
            var groupId = $('#groupId').val();
            // 聚焦风险
            var initialFocusRisk = $('#initialFocusRisk').val();
            //风研编号
            var achievementCode = $('#achievementCode').val();

            var data = {
                // "achievementId":id,
                "remoteYear": remoteYear,
                "spanType": spanType,
                "topicName": topicName,
                "subTopicName": subTopicName,
                "person": principalName,
                "groupId": groupId?groupId:'',
                "initialFocusRisk": initialFocusRisk,
                "achievementCode":achievementCode,
                "bulletinId":id,
                "projectId":project_id
            };
            return data;
        }


        // 查询
        $('#searchBtn').click(function () {
            timeLimitData();
        });

        // 重置
        $('#resetBtn').click(function () {
            // 可选：手动触发 reset 表单事件
            $('#prjSendInfoSearch')[0].reset(); // 重置整个表单

            // 如果有 select 需要重置，使用 form.render()
            $("#spanTypeName").val(''); // 来源
            $("#groupId").val('');      // 团队
            form.render();
            timeLimitData();
        });

        // 年度
        laydate.render({
            elem: '#remoteYear', //指定元素
            type: 'year',
            format: 'yyyy',
            value: '',
            isInitValue: true,
            showBottom: true, //关闭底部框 去掉取消、确定、清空按钮
            max: '', //指定最大年份为当年
            done: function (value, date) {

            }
        });

        /************保存 **************/
        window.saveTask = function(){
             var checkedRows = table.checkStatus("infoTable").data;
             //只取checkedRows中的id
              var ids = [];
              for(var i=0;i<checkedRows.length;i++){
                  ids.push(checkedRows[i].researchInfoId);
              }
             if(checkedRows.length==0){
                 frm.validate("请选择需要保存的数据！");
                 return false;
             }
            var indexZG = layer.load(3, {//遮盖层
                shade: [0.4, '#fff'] //0.4透明度的白色背景
            });
            var url = ctx + "/pro/auditbulletin/batchSaveRiskApplyInfo";
            if(project_id){
                url = ctx + "/pro/auditbulletin/batchSaveProjectApplyInfo";
            }
            $.ajax({
                url: url,
                dataType: "JSON",
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                data: JSON.stringify({
                    "achievementId":id,
                    "projectId":project_id,
                   "researchInfoIds":ids
                }),
                success: function (res) {
                    layer.close(indexZG);
                    if (res.httpCode == 200) {
                        layer.msg('保存成功！', { icon: 1, time: 2000 }, function () {
                            window.parent.layer.close(parent.layer.getFrameIndex(window.name));
                            window.parent.riskTableFun('edit')
                        });
                    }else {
                        frm.error(res.msg);
                    }
                }
            });
        };


        //关闭弹窗方法
        window.closePage = function() {
            var index = parent.layui.layer.getFrameIndex(window.name);
            parent.layui.layer.close(index);
        }

        $(function () {
            //更多查询
            $('.btnMore').on('click', function () {
                var me = $(this), childDwon = me.children('.slide-down'), childUp = me.children('.slide-up');
                if (childDwon.hasClass('none')) {
                    childDwon.removeClass('none');
                    childUp.addClass('none');
                    me.find('span').text("搜索更多");
                    $('.search-more').stop().hide();
                } else {
                    childDwon.addClass('none');
                    childUp.removeClass('none');
                    me.find('span').text("收起更多");
                    $('.search-more').stop().show();
                }
            });
        });

    });
</script>

</html>
