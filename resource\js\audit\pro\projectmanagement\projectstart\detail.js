/**
 * 项目启动
 * @returns
 */
 var reloadFlile ="";
 var workTypeOptions = "<option value=''>请选择</option>\n";
 var tmpData={};
 var tabflag=0;
 var remarkValS = [];
 layui.use(['jqform', 'table', 'jqbind', 'layer', 'jquery','laytpl','element'], function() {
        var $ = layui.jquery,
            form = layui.jqform,
            table = layui.table,
            ctx = top.global.ctx,
            jqbind = layui.jqbind,
            tpl = layui.laytpl,
            layer = layui.layer,
            tpl = layui.laytpl,
            element = layui.element,
            ctxStatic = top.global.ctxStatic;
        $(".layui-colla-title").click(function () {
    		//$(this).siblings(".layui-colla-content").toggleClass("layui-show");
    		layuiShowTitle(this);
    	});
        var urlParam ={
        		businessKey:getUrlParam("businessKey"),
        		processInstanceId:getUrlParam("processInstanceId"),
        		taskDefinitionKey:getUrlParam("taskDefinitionKey"),
        		taskId:getUrlParam("taskId"),
        		executionId:getUrlParam("executionId"),
        		startLink:getUrlParam("startLink"),
        		endLink:getUrlParam("endLink")
        };

        // 监听Tab切换，以改变地址hash值
        element.on('tab(fileTabBrief)', function (data) {
        	tabflag = data.index;
        	reloadFlile(tabflag);
        });

        var param = {
        		projectId:urlParam.businessKey,
        		procInsId:urlParam.processInstanceId
        };
        var urlStr = top.global.ctx + "/projectStart/start/getProjectInfo";
        var jqajax = layui.jqajax,
		ajax = new jqajax();
		ajax.options.url = '/projectStart/start/getProjectWokeType';
		ajax.ajax(ajax.options);
		ajax.complete = function (ret, options) {
            if (ret.httpCode == 200) {
                // 获取后台返回的数据，数据格式为code和codeText
                //for (var x in ret.data) {
                //	workTypeOptions += '<option value = "' + ret.data[x].CODE + '">' + ret.data[x].CODETEXT + '</option>\n';
                //}
                remarkValS = ret.data;
                //初始化项目信息
                loadData(urlStr,param);
            }
        };
		 window.openWarningSheetPanel = function (warningSheetId) {
			 layer.open({
				 type: 2,
				 offset: 'auto',
				 content:"views/audit/online/waringsheet/sheet/info.html?id=" + warningSheetId,
				 title: '预警单信息',
				 area: ['90%', '90%']
			 });
		 };
        function loadData(urlStr,param){
        $.post(urlStr, param,
            function (data) {
        	var record = data.data;
			auditOrgId = record.auditOrgId;
				if(auditOrgId=='001000'){
					$('#audit-prompt').show()
				}else{
					$('#audit-prompt').hide()
				}
        	urlParam.businessKey = record.projectId;
        	$("#projectName").val(record.projectName);
        	$("#projectNum").val(record.projectNum);
        	$("#planProjectId").val(record.planProjectId);
        	$("#projectId").val(record.projectId);
        	$("#projectTypeEnumName").val(record.projectTypeEnumName);
        	$("#projectOrgName").val(record.projectOrgName);
        	$("#projectOrgId").val(record.projectOrgId);
        	$("#specialAuditFlag").val(record.specialAuditFlag=='1'?"是":"否");
        	$("#remoteFlag").val(record.remoteFlag=='1'?"是":"否");
			// 设置是否关联风研成果单选按钮
			$("input[name='isAssociation'][value='" + record.isAssociation + "']").prop("checked", true);
				if(record.isAssociation=='1'){
					$('#riskList').show()
					riskTableFun();
				}
				weightList = record.weightList;
				lp(tpl, weightList, $("#group-weight-tpl").html(), $("#weightList"));

				if(record.remoteFlag=='1'){
				_floatballbar('<br>远程数据<br/>分析成果','','',{left:950,top:10},{w:60,h:60});
			}

			if ("12" === record.projectTypeEnumId) {
				$.ajax({
					url: ctx + "/online/warningWorkRelation/planProjectRelatedWarningAndOrder/" + record.planProjectId,
					type: "POST",
					dataType: "JSON",
					contentType: "application/json;charset=UTF-8",
					success: function (data) {
						table.render({
							id: 'warningSheetTable',
							elem: '#warningSheetTable',
							page: false,
							limit: 100,
							even: true,
							data: data.relatedWarningRecords,
							cols: [[
								{type: 'numbers', title: '序号', width: '10%', align: 'center', style: 'text-align: center;'},
								{field: 'warningSheetName', title: '预警单名称', width: '60%', align: 'center', style: 'text-align: left;', templet: function (d) {
									return "<a href='javascript: void(0);' onclick='openWarningSheetPanel(\"" + d.warningSheetId + "\");' " +
										"style='text-decoration: underline;'>" + d.warningSheetName + "</a>";
								}},
								{field: 'warningSheetTeam', title: '所属团队', width: '15%', align: 'center', style: 'text-align: left;'},
								{field: 'warningSheetDept', title: '对口处室', width: '15%', align: 'center', style: 'text-align: left;'}
							]]
						});

						table.render({
							id: 'checkWorkOrderTable',
							elem: '#checkWorkOrderTable',
							page: false,
							limit: 100,
							even: true,
							data: data.relatedCheckOrderRecords,
							cols: [[
								{type: 'numbers', title: '序号', width: '10%', align: 'center', style: 'text-align: center;'},
								{field: 'checkOrderName', title: '核查工单名称', width: '60%', align: 'center', style: 'text-align: left;'},
								{field: 'initiateUnitName', title: '发起单位', width: '15%', align: 'center', style: 'text-align: left;'},
								{field: 'orderCreateTime', title: '创建时间', width: '15%', align: 'center', style: 'text-align: center;'}
							]]
						});

						$("#warningSheetPanel").show();
						$("#checkWorkOrderPanel").show();
					}
				});
			}
			form.render()
        	loadTable();
			loadFileTable();
            }, "json");
        }

       var loadIndex = layer.load(0, {shade: false});
       function loadTable(){
        var tableIns = table.render({
        	elem: '#groupMemberList',
            url :top.global.ctx + '/projectStart/start/queryGroupMemberList',
            id: 'groupMemberList',
			even: true, //开启隔行背景
            cols: [[
                {type: 'numbers', title: '序号', width: '5%', align: 'center'},
                {field: 'userName', title: '项目成员', width: '10%', align: 'center', style: 'text-align: center'},
                {field: 'codeText', title: '项目角色', width: '10%', align: 'center'},
                {field: 'projectWokeTypeEnumId', title: '组内分工', width: '30%', align: 'center', style: 'text-align: left', templet: '#projectWokeTypeEnumIdRadio'},
                {field: 'projectWokeFocus', title: '分工信息', width: '38%',edit:'text', align: 'center', style: 'text-align: center'},
                {title: '操作', width: '6%', align: 'center', style: 'text-align: center', toolbar: '#memberstoolBar'}
            ]],
            where: {
            	projectId:urlParam.businessKey
	        },
            done: function(res, curr, count) {
            	$(".layui-form-item").css("margin-bottom","0px");
            	tmpData=res.data;
            	$("td[data-field='projectWokeTypeEnumId']").each(function(){
            		$(this).find(".layui-table-cell").removeClass('layui-table-cell');
        		});
                layer.close(loadIndex);
            }
        });
       }
        var rowsData={};
        /**
         * 监听单选框 specialtyAbilityEnumId 专业胜任能力
         */
        form.on('radio(projectWokeTypeEnumId)', function (obj) {
            //radio 的取值 var value = obj.value;
            //行记录的值
            var data = JSON.parse(obj.elem.getAttribute("data"));
            var memberId = data.memberId;
            for (var x in tmpData) {
            	if (tmpData[x].memberId==memberId) {
            		tmpData[x].projectWokeTypeEnumId=obj.value;
				}
            }
            rowsData[memberId]={
			    memberId :memberId,
     			projectWokeTypeEnumId:obj.value,
     			projectWokeFocus:data.projectWokeFocus
	    	}
        });
        table.on('edit(groupMemberList)', function(obj){ //注：edit是固定事件名，test是table原始容器的属性 lay-filter="对应的值"
        	var projectWokeTypeEnumId=rowsData[obj.data.memberId].projectWokeTypeEnumId;
        	for (var x in tmpData) {
            	if (tmpData[x].memberId==obj.data.memberId) {
            		tmpData[x].projectWokeFocus=obj.value;
				}
            }
        	rowsData[obj.data.memberId]={
			    memberId :obj.data.memberId,
     			projectWokeTypeEnumId:projectWokeTypeEnumId,
     			projectWokeFocus:obj.value
	    	}
        });
        table.on('tool(groupMemberList)',function(obj){
        	 if(obj.event=='save'){
        		  delete rowsData[obj.data.memberId];
        		  for (var x in tmpData) {
                  	if (tmpData[x].memberId==obj.data.memberId) {
                  		saveGroupMember(tmpData[x]);
      				}
                  }
        	  }
        });

        function saveGroupMember(rowdata){
       //	var loadIndex = layer.load(0, {shade: false});
        	if(rowdata.projectRoleEnumId=="10008005" || rowdata.projectRoleEnumId=="10008006"){
        		if(rowdata.projectWokeTypeEnumId != "4"){
        			layer.msg('IT人员组内分工必须选择IT！', {icon: 2,time: '2000'});
        			return false;
        		}
        	}
        	if(rowdata.projectWokeTypeEnumId == ""){
        		layer.msg('请为'+rowdata.userName+'选择组内分工！', {icon: 2,time: '2000'});
    			return false;
        	}
       	var record = {
       			memberId :rowdata.memberId,
       			projectWokeTypeEnumId:rowdata.projectWokeTypeEnumId,
       			projectWokeFocus:rowdata.projectWokeFocus
       	};
       	$.ajax({
       	    type: 'post',
       	    url: top.global.ctx + '/projectStart/start/saveGroupMember',
       	    dataType: "json",
       	    data:record,
       		success: function(data){
       			layer.msg('保存成功！', {icon: 1,time: '200'});

       	    },
       	    error: function(XMLHttpRequest, textStatus, errorThrown){
       	    	layer.close(loadIndex.subindex);
       	    	layer.msg('保存失败！服务器异常，请稍后再试', {icon: 2,time: '2000'}, function () {
       	    		var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
 		    			 parent.layer.close(index); //再执行关闭
               	});
       	    }
       	});
       	reloadgroupMemberList();
       }
        function reloadgroupMemberList(){
    		//reloadTable('subproject',{planProjectId:$("#planProjectId").val()});
    		table.reload('groupMemberList', {
    			where : {projectId:urlParam.businessKey},
    			done: function(res, curr, count) {
                	tmpData=res.data;
                	if(res.count>10){
    	        		 layui.table.reload(this.id,{
    	        			//height:324,
    	        			done:function(res){
    	        				$("select[name='workType']").html(workTypeOptions);
    	        				layui.each($("select[name='workType']"), function (index, item) {
    	        					var elem = $(item);
    	        					elem.val(elem.data('value'));
    	        				});
    	        				form.render('select');
    	        			}
    	                 });
    	        	}else{
    	        		$("select[name='workType']").html(workTypeOptions);
    	        		layui.each($("select[name='workType']"), function (index, item) {
    	        			var elem = $(item);
    	        			var d=elem.data;
    	        			elem.val(elem.data('value'));
    	        		});

    	        		form.render('select');
    	        	}

                    //layer.close(loadIndex);
                }
    		});
    	}
        bindClick('saveGroupMembers',saveGroupMembers);
        bindClick('_floatballbar',openFloatBallBar);
        function openFloatBallBar(){
        	var param = 'planproject='+$("#planProjectId").val()+'&projectOrgId='+$("#projectOrgId").val();
        	var indexs = top.layer.open({
                type: 2,
                offset: '20px',
                content:"views/audit/pro/projectmanagement/report/analyze.html?"+param,
                title: '远程数据分析成果',
                area:['90%', '90%'],
                fixed: true/*,
                btn: ['关闭'],
                skin: 'demo-class',
                cancel:function(){
 				   //reloadPretrial();
                },
 			   yes:function(index,layero){
 				   //reloadPretrial();
 				  var index = parent.layer.getFrameIndex(window.name);
 				  parent.layer.close(indexs);
 			   }*/

            });

        }
        //绑定点击事件
        function bindClick(id,method){
        	 $(document).on('click','#'+id,method);
        }
        window.getPassVide = function (){
        	//reloadgroupMemberList();
//        	$('#saveGroupMembers').click();
//        	var bool = false;
//           	var userNames="";
//        	$.each(tmpData,function(k,v){
//           			if((v.projectRoleEnumId=="10008005" || v.projectRoleEnumId == "10008006") && v.projectWokeTypeEnumId != "4"){
//           				bool = true ;
//           				return ;
//           			}
//           			if(v.projectWokeTypeEnumId == ""){
//           				if(userNames != "" && v.userName != ""){
//               				userNames = userNames + ","+v.userName;
//               			}else{
//               				userNames = v.userName;
//               			}
//           			}
//           	});
//        	if(userNames != ""){
//        		parent.layer.msg('请为'+userNames+'进行组内分工！', {icon: 2,time: '4000'});
//        		parent.layer.close(loadIndex);
//    			return false;
//           	}
//           	if(bool){
//           		parent.layer.msg('IT人员组内分工必须选择IT！', {icon: 2,time: '2500'});
//           		parent.layer.close(loadIndex);
//    			return false;
//           	}

        };
        function saveGroupMembers(){
           	var loadIndex = layer.load(0, {shade: false});
           	//var params= new Array();
           	/*$.each(rowsData,function(k,v){
           		params.push(v);
           	})*/
           	passStatus=true;
           	var bool = false;
           	var userNames="";
           	$.each(tmpData,function(k,v){
           		if((v.projectRoleEnumId=="10008005" || v.projectRoleEnumId == "10008006") && v.projectWokeTypeEnumId != "4"){
       				bool = true ;
       				//return ;
       			}
       			if(v.projectWokeTypeEnumId == ""){
       				if(userNames != "" && v.userName != ""){
           				userNames = userNames + ","+v.userName;
           			}else{
           				userNames = v.userName;
           			}
       			}

           	});

           	if(userNames != ""){
           		layer.msg('请为'+userNames+'进行组内分工！', {icon: 2,time: '4000'});
           		layer.close(loadIndex);
           		passStatus=false;
    			return false;
           	}
           	if(bool){
           		layer.msg('IT人员组内分工必须选择IT！', {icon: 2,time: '2500'});
           		layer.close(loadIndex);
           		passStatus=false;
    			return false;
           	}
           	var groupMember ={
           			paramList:tmpData
           	};

           	$.ajax({
           	    type: 'post',
           	    url: top.global.ctx + '/projectStart/start/saveGroupMembers',
           	    dataType: "json",
           	    data:groupMember,
           		success: function(data){
           				layer.close(loadIndex);
           				rowsData={};
          			   var index = layer.getFrameIndex(window.name); //先得到当前iframe层的索引
     	    			 	layer.close(index); //再执行关闭
     				 	var frames = window.parent.window.document.getElementById("editGroup");
     				 	layer.msg('保存成功！', {icon: 1,time: '200'});
     				 	reloadgroupMemberList();
           	    },
           	    error: function(XMLHttpRequest, textStatus, errorThrown){
           	    	layer.close(loadIndex);
           	    	passStatus=false;
           	    	layer.msg('保存失败！服务器异常，请稍后再试', {icon: 2,time: '2000'}, function () {
           	    		var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
     		    			 parent.layer.close(index); //再执行关闭
                   	});
           	    }
           	});
           }

       function  loadFileTable(){
        //加载附件列表
   	  table.render({
            elem: '#filestable',
            url :top.global.ctx + '/projectPrepare/prepare/getFileList',
            id: 'filestable',
		    even: true, //开启隔行背景
            cols: [[
               // {type: 'numbers', title: '序号', width: '5%', align: 'center', fixed: 'left'},
                {field: 'proAttName', title: '文档名称', width: '25%', align: 'center', style: 'text-align: left'},
                {field: 'proAttTypeName', title: '文档类型', width: '25%', align: 'center', style: 'text-align: left'},
                {field: 'userName', title: '上传人', width: '14%', align: 'center', style: 'text-align: center'},
                {field: 'createTime', title: '上传时间', width: '20%', align: 'center', style: 'text-align: center'},
                {title: '操作', width: '15%', align: 'center', style: 'text-align: center', toolbar: '#operatorBar'}
            ]],
            page:false,
            where: {
            	objectId:$("#planProjectId").val(),
            	proStage:1,
            	type    :'SJXMWJLX'
   	        },
            done: function(res, curr, count) {
            	if(res.count>9){
   	        		 layui.table.reload(this.id,{
   	        			height:300,
   	        			done:function(res){
   	        			}
   	                 });
   	        	}
            }
        });
	 }
       window.saveOrDelDownLoad = function (at,objectId,proAttachmentId,releaseId){
    	   //var i = at.children[0];
    	   //i.parentElement.removeChild(i);
    	   //var iNew = document.createElement("i");
    	  // at.innerHTML = '<i class="iconfont">&#xe73f;</i>';
    	   //at.appendChild(iNew);
    	   var flag = "0";
    	   if(at.title == "下发"){
    		   flag = "0";
    	   }else{
    		   flag = "1";
    	   }
    	   var record = {
                   projectId :objectId,
                   proAttachmentId:proAttachmentId,
                   releaseId:releaseId
               };
    	   addOrUpdateAttachmentRelease(at,record,flag);
       }
       /*table.on('tool(filestable)',function(obj){
           if(obj.event=='save'){
               addOrUpdateAttachmentRelease(obj,'0');
           }
           if(obj.event=='delDownLoad'){
        	   addOrUpdateAttachmentRelease(obj,'1');
           }
       });*/
          function addOrUpdateAttachmentRelease(at,record,flag) {
              if(flag=='0'){
	              $.ajax({
	                  type: 'post',
	                  url: top.global.ctx + '/projectStart/start/addAttachmentRelease',
	                  dataType: "json",
	                  async:false,
	                  data:record,
	                  success: function(data){
	                      layer.msg('文件下发成功！',{icon: 1,time: '2000'}, function () {
	     				 });
	                      var i = at.children[0];
	               	   	  i.parentElement.removeChild(i);
	               	      at.innerHTML = '<i class="iconfont">&#xe73f;</i>';
	               	      at.title = "取消下发";
	                  },
	                  error: function(XMLHttpRequest, textStatus, errorThrown){
	                      layer.close(loadIndex.subindex);
	                      layer.msg('文件下发失败！服务器异常，请稍后再试', {icon: 2,time: '2000'}, function () {
	                          var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
	                          parent.layer.close(index); //再执行关闭
	                      });
	                  }
	              });
	              //table.reload("filestable");
              }else{
            	  $.ajax({
	                  type: 'post',
	                  url: top.global.ctx + '/projectStart/start/delAttachmentRelease',
	                  dataType: "json",
	                  async:false,
	                  data:record,
	                  success: function(data){
	                      layer.msg('文件取消下发成功！',{icon: 1,time: '2000'}, function () {
		     				 });
	                      var i = at.children[0];
	               	   	  i.parentElement.removeChild(i);
	               	      at.innerHTML = '<i class="iconfont">&#xe67b;</i>';
	               	      at.title = "下发";
	                  },
	                  error: function(XMLHttpRequest, textStatus, errorThrown){
	                      layer.close(loadIndex.subindex);
	                      layer.msg('文件取消下发失败！服务器异常，请稍后再试', {icon: 2,time: '2000'}, function () {
	                          var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
	                          parent.layer.close(index); //再执行关闭
	                      });
	                  }
	              });
            	  //table.reload("filestable");
              }
          }
          var issueFilesFalg = 0;
          var issueFilesData = "";
          bindClick('issueFiles',issueFiles);
          function issueFiles(){
        	  if(issueFilesFalg == 1){
        		  issueFilesData = {objectId:$("#projectId").val(),proStage:1,type    :'SJXMWJLX'};
        	  }else{
        		  issueFilesData = {objectId:$("#planProjectId").val(),proStage:1,type    :'SJXMWJLX'};
        	  }
        	  $.ajax({
                  type: 'post',
                  url: top.global.ctx + '/projectStart/start/saveReleaseList',
                  dataType: "json",
                  async:false,
                  data:issueFilesData,
                  success: function(data){
                      layer.msg('文件批量下发成功！',{icon: 1,time: '2000'}, function () {
	     				 });
                  },
                  error: function(XMLHttpRequest, textStatus, errorThrown){
                      layer.close(loadIndex.subindex);
                      layer.msg('文件下发失败！服务器异常，请稍后再试', {icon: 2,time: '2000'}, function () {
                          var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
                          parent.layer.close(index); //再执行关闭
                      });
                  }
              });
        	  table.reload("filestable");
          }
	    reloadFlile = function (index){
   	 		if(index==0){
   	 			issueFilesFalg = 0;
				reloadTable('filestable',{
					objectId:$("#planProjectId").val(),
					proStage:1,
					type    :'SJXMWJLX'
				});
			}else{
				issueFilesFalg = 1;
				reloadTable('filestable',{
					objectId:urlParam.businessKey,
					proStage:1,
					type    :'SJXMWJLX'
				});
			}
		};

	 function reloadTable(tableId,param){
		 table.reload(tableId, {
			 /*page : {
                 curr : 1
             // 重新从第 1 页开始
             },*/
			 height: 'full-160',
			 where : param,
			 done : function(res, curr, count) {
				 if(res.count>10){
					 layui.table.reload(this.id,{
						 height:300,
						 done:function(res){
						 }
					 });
				 }
			 }
		 });
	 }

    });


