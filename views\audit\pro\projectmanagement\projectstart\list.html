<!--#include virtual ="include/header.html"-->
<head>
    <link href="../../../../../resource/css/audit/pro/projectmanagement/common/floatballbar.css" media="all"
          rel="stylesheet" type="text/css">
    <link href="../../../../../resource/css/audit/pro/showselect.css" media="all" rel="stylesheet" type="text/css">
    <style type="text/css">
        .layui-table-body {
            overflow-x: hidden;
        }

        /* 设置下拉框的高度与表格单元相同 */
        .layui-table tbody tr td .layui-input, .layui-textarea {
            height: 60px !important;
        }
        /* 合并第三个样式开始 */

        .weight-list {
            display: flex;
            border: 1px solid #ddd;
            flex-wrap: wrap;
            padding: 6px 6px 0;
        }

        .weight-list .weight-li {
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 6px;
        }

        .weight-list .weight-li-label {
            display: inline-block;
            margin-right: 4px;
        }

        .weight-list .weight-li-input {
            display: inline-block;
            width: 70px;
        }


    </style>
</head>
<!-- 项目启动 -->
<body style="background-color:white">
<div class="layui-fluid larry-wrapper">
    <section class="panel panel-padding" style="height:100%">
        <form class="layui-form layui-form-pane form-conmon form-conmon-more">
            <div class="layui-collapse model-collapse queryCondition">
                <div class="layui-colla-item model-item">
                    <h2 class="layui-colla-title">项目来源</h2>
                    <div class="layui-colla-content layui-show">
                        <div class="layui-form-item layui-form-item-sm">
                            <div class="layui-row layui-col-space10">
                                <div class="layui-col-md4 layui-col-sm4 layui-col-lg4 ">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label must-icon" style="width: 127px;">是否关联风研成果</label>
                                        <div class="layui-input-block">
                                            <div class="layui-col-xs9">
                                                <input name="isAssociation" lay-filter="isAssociation" title="是" type="radio" value="1"/>
                                                <input name="isAssociation" lay-filter="isAssociation" title="否" type="radio" value="0"/>

                                            </div>
                                        </div>
                                    </div>

                                </div>

                                <div class="layui-row contents" id="riskList" style="display: none" >
                                    <div class="layui-col-md12 layui-col-sm12 layui-col-lg12 new-style">
                                        <div class="layui-form-item layui-form-item-sm"  style="margin-bottom:0;">
                                            <label class="layui-form-label must-icon">对应风研成果</label>
                                            <button class="layui-btn layui-btn-sm layui-btn-normal"
                                                    type="button" onClick="selectRisk()">
                                                <i class="iconfont search-icon">&#xe635;</i> 选择风研成果
                                            </button>
                                            <span class="text-red" style="margin-left:10px;">操作提醒：多次选择会以最后一次选择的数据为准，请知晓！</span>
                                        </div>
                                    </div>
                                    <div class="layui-col-md12 layui-col-sm12 layui-col-lg12 new-style">
                                        <div class="layui-form-item layui-form-item-sm">
                                            <label class="layui-form-label"></label>
                                            <div class="layui-input-block">
                                                <table class="layui-table jq-even" id="riskTable" lay-filter="riskTable"></table>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md12 layui-col-sm12 layui-col-lg12 new-style">
                                        <div class="layui-form-item layui-form-item-sm">
                                            <label class="layui-form-label must-icon">风研成果权重</label>
                                            <div  class="layui-input-block weight-list" id="weightList">

                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md12 layui-col-sm12 layui-col-lg12 new-style">
                                        <div class="layui-form-item layui-form-item-sm"  style="margin-bottom:0;">
                                            <label class="layui-form-label "></label>
                                            <span class="text-red" style="margin-left:10px;">操作提醒：该权重为各风研成果形成通报、管理建议、风险提示函时关联度占比，请根据实际情况填写，权重之和可小于100。</span>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--#include virtual="views/audit/pro/projectmanagement/projectInfo.html"-->
            <div class="layui-collapse model-collapse queryCondition">
                <div class="layui-colla-item model-item">
                    <h2 class="layui-colla-title">组内成员分工</h2>
                    <div class="layui-colla-content layui-show">
                        <div class="layui-form-item layui-form-item-sm">
                            <div class="layui-row layui-col-space10">
                                <div class="layui-show">
                                    <span class="layui-btn layui-btn-sm btn-outline" id="saveGroupMembers"
                                          style="float: right; display: inline"><i
                                            class="iconfont search-icon">&#xe6c5;</i> 批量保存</span>
                                    <table class="layui-table" id="groupMemberList"
                                           lay-filter="groupMemberList"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="audit-prompt" class="layui-collapse model-collapse queryCondition"  style="display: none;padding:6px 0 0 10px;font-weight: bold;border-width: 0">
                <span class="text-red">底稿注意事项：</span>
                <span> 为远程风研成果应用准确，编写底稿时需注意，填写最新版底稿模板中“风研成果编号”，编号可参照【审计风研中台】-【审计中台】-【风研成果】-【省分线索库】中的序号。若涉及多条风研成果，编号间用“，”分割，若非风研成果触发，请填"无"。请各位同事使用最新版底稿模板（点击【下载模板】下载最新版底稿模板），并落实现场发现问题与风研成果关联。</span>
            </div>

            <div class="layui-collapse model-collapse queryCondition">
                <div class="layui-colla-item model-item">
                    <h2 class="layui-colla-title">审前准备资料</h2>
                    <div class="layui-colla-content layui-show">
                        <div class="layui-form-item layui-form-item-sm">
                            <div class="layui-row layui-col-space10">
                                <!-- <div class="layui-show">
                                    <table class="layui-table jq-even" id="subproject" lay-filter="subproject"></table>
                                </div> -->
                                <div class="layui-tab layui-tab-brief" lay-filter="fileTabBrief">
                                    <ul class="layui-tab-title" style="border:none">
                                        <li class="layui-this" style="border:none">父项目资料</li>
                                        <li style="border:none">子项目资料</li>
                                    </ul>
                                    <span class="layui-btn layui-btn-sm btn-outline" id="issueFiles"
                                          style="float: right; display: inline"><i
                                            class="iconfont search-icon">&#xe6c4;</i> 批量下发</span>
                                    <table class="layui-table jq-even" id="filestable" lay-filter="filestable"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-collapse model-collapse queryCondition" id="warningSheetPanel" style="display: none;">
                <div class="layui-colla-item model-item">
                    <h2 class="layui-colla-title">预警单信息</h2>
                    <div class="layui-colla-content layui-show">
                        <table class="layui-table" id="warningSheetTable" lay-filter="warningSheetTable"></table>
                    </div>
                </div>
            </div>
            <div class="layui-collapse model-collapse queryCondition" id="checkWorkOrderPanel" style="display: none;">
                <div class="layui-colla-item model-item">
                    <h2 class="layui-colla-title">核查工单信息</h2>
                    <div class="layui-colla-content layui-show">
                        <table class="layui-table" id="checkWorkOrderTable" lay-filter="checkWorkOrderTable"></table>
                    </div>
                </div>
            </div>
        </form>
    </section>
</div>
<input id="planProjectId" type="hidden">
<input id="projectId" type="hidden">
<script id="operatorBar" type="text/html">
    <!--<a class="table-btn" lay-event="detail" title="下载">
        <i class="iconfont search-icon">&#xeac3;</i>
    </a>
    <a class="table-btn" lay-event="showfile" title="word预览">
        <i class="iconfont">&#xe686;</i>
    </a>-->
    {{# if(d.releaseFlag=='' || d.releaseFlag == '0'){ }}
    <a class="table-btn" lay-event="save" title="下发"
       onclick="saveOrDelDownLoad(this,'{{ d.objectId }}','{{ d.proAttachmentId }}','{{ d.releaseId }}')">
        <i class="iconfont">&#xe67b;</i>
    </a>
    {{# } else { }}
    <a class="table-btn" lay-event="delDownLoad" title="取消下发"
       onclick="saveOrDelDownLoad(this,'{{ d.objectId }}','{{ d.proAttachmentId }}','{{ d.releaseId }}')">
        <i class="iconfont">&#xe73f;</i>
    </a>
    {{# } }}
</script>
<script id="memberstoolBar" type="text/html">
    <a class="table-btn" lay-event="save" title="保存">
        <i class="iconfont">&#xe6a7;</i>
    </a>
</script>
<script id="selectWorkType" type="text/html">
    <select name="workType" id="workType_{{d.memberId}}" lay-filter="workType" data-value="{{d.projectWokeTypeEnumId}}"
            lay-search="">
    </select>
</script>
<script id="projectWokeTypeEnumIdRadio" type="text/html">
    {{#
    for (var i in remarkValS){
    }}
    {{# if(remarkValS[i].CODE != undefined){ }}
    {{# if(d.projectWokeTypeEnumId == remarkValS[i].CODE){
    if(i%3==2){ }}
    <input type="radio" data='{{JSON.stringify(d)}}' name="radio_{{d.memberId}}" value="{{remarkValS[i].CODE}}"
           lay-filter="projectWokeTypeEnumId" checked title="{{remarkValS[i].CODETEXT}}"><br>
    {{# }else{ }}
    <input type="radio" data='{{JSON.stringify(d)}}' name="radio_{{d.memberId}}" value="{{remarkValS[i].CODE}}"
           lay-filter="projectWokeTypeEnumId" checked title="{{remarkValS[i].CODETEXT}}">
    {{# } }}
    {{# }else {
    if(i%3==2){ }}
    <input type="radio" data='{{JSON.stringify(d)}}' name="radio_{{d.memberId}}" value="{{remarkValS[i].CODE}}"
           lay-filter="projectWokeTypeEnumId" title="{{remarkValS[i].CODETEXT}}"><br>
    {{# }else{ }}
    <input type="radio" data='{{JSON.stringify(d)}}' name="radio_{{d.memberId}}" value="{{remarkValS[i].CODE}}"
           lay-filter="projectWokeTypeEnumId" title="{{remarkValS[i].CODETEXT}}">
    {{# } }}
    {{# } }}
    {{# } }}
    {{# } }}
</script>
<!--#include virtual="views/audit/pro-online-common/layuiShow.html"-->
<!--#include virtual ="include/tpl/select-cat.html"-->
<!--#include virtual ="include/version.html"-->
<!--   <button class="layui-btn layui-btn-sm" lay-event="add">添加</button>
        <button class="layui-btn layui-btn-sm" lay-event="delete">删除</button>
        <button class="layui-btn layui-btn-sm" lay-event="update">编辑</button> -->
<script src="../../../../../resource/js/audit/pro/projectmanagement/projectstart/detail.js?v=1.0"></script>
<script src="../../../../../resource/js/audit/pro/common/floatballbar.js"></script>
<script src="../../../../../resource/js/audit/pro/common/usercache.js?v=6.5"></script>
<script src="../../../../../resource/js/audit/pro/common/common.js"></script>
<script>
    layui.use('element', function () {
        var $ = layui.jquery
            , element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块
        element.on('tab(test)', function (elem) {
            reloadFlile(elem.index);
        });
    });
</script>
<script id="group-weight-tpl" type="text/html">
    {{# layui.each(d, function(index, item){ }}
    <div class="weight-li">
        <span class="weight-li-label">{{item.achievementCode}}</span>
        <input class="layui-input weight-li-input" onkeyup="upperCase(this)" code="{{item.researchInfoId}}"  type="text"
               value="{{item.weight}}">
    </div>
    {{# }); }}
</script>
<!-- 流程推进后自定义展示下环节人模块 -->
<script type="text/javascript">
    var userPostId;
    var form;
    var project_id = getUrlParam("businessKey");
    var riskList = [];
    var typeDictList = [];
    var weightList = [];//权重
    layui.use(['jqform', 'table', 'jqbind', 'layer', 'jquery','laytpl','jqfrm'], function () {
        var $ = layui.jquery,
        form = layui.jqform,
        table = layui.table,
            frm = layui.jqfrm,
            tpl = layui.laytpl;

        ajaxJsonPost(top.global.ctx + '/morder/query/userinfo', {}, function (ret) {
            userInfoMap = ret.userInfoMap;
            userPostId = userInfoMap.post.id;
        });

        //风研成果表格
        window.riskTableFun = function(type) {
            table.render({
                elem: '#riskTable',
                id: 'riskTable',
                url: ctx + "/pro/auditbulletin/queryRiskListByProjectId",
                where: {projectId:project_id,isSelected:true},
                page: false,
                limit: 9999,
                even: true,
                cols: [[
                    { type: 'numbers', title: '序号', width: '5%' },
                    { field: 'achievementCode', title: '风研编号', width: '8%', align: 'center' },
                    { field: 'remoteYear', title: '年度', width: '8%', align: 'center' },
                    {
                        field: 'spanType',
                        title: '来源',
                        width: '8%',
                        align: 'center',
                        templet: function (d) {
                            return fromatComon(d.spanType,typeDictList);
                        }
                    },
                    { field: 'topicName', title: '专题', width: '9%', align: 'center', style: 'text-align: left;' },
                    { field: 'subTopicName', title: '子专题', width: '9%', align: 'center', style: 'text-align: left;' },
                    { field: 'initialFocusRisk', title: '聚焦风险', width: '30%', align: 'center', style: 'text-align: left;' },
                    { field: 'chargePersonName', title: '负责人', width: '7%', align: 'center' },
                    { field: 'analystPersonName', title: '分析人', width: '8%', align: 'center' },
                    { field: 'groupName', title: '团队', width: '8%', align: 'center' }
                ]],
                done: function(res) {
                    riskList = res.data;
                    if(type=='edit'||weightList.length==0){//修改
                        weightListFun()
                    }

                }
            });
        };

        /**
         * 是否关联风研成果
         */
        form.on('radio(isAssociation)', function (data) {
            if (data.value == "0") {
                if (riskList.length) {
                    layer.confirm('已经存在关联数据，是否删除。', {
                        icon: 3,
                        title: '提示'
                    }, function (index) {
                        // 用户点击了“是”
                        layer.close(index)
                        //调用删除方法
                        $('#riskList').hide()
                        updateAssociation('0')
                        batchDelRiskApplyInfo()
                    }, function (index) {
                        // 用户点击了“否”
                        layer.close(index);
                        $("input[name='isAssociation'][value='1']").prop("checked", true);
                        form.render('radio'); // 只渲染 radio 类型即可
                    });
                } else {
                    $('#riskList').hide()
                    updateAssociation('0')
                }
            } else {
                $('#riskList').show()
                riskTableFun('edit')
                updateAssociation('1')
            }
        });

        //保存 是否关联风研成果
        window.updateAssociation  = function (isAssociation) {
            $.ajax({
                url: ctx + "/projectStart/start/updateProjectById",
                dataType: "JSON",
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                data: JSON.stringify({
                    "projectId": project_id,
                    "id": project_id,
                    "isAssociation":isAssociation
                }),
                success: function (res) {
                    if (res.httpCode == 200) {
                    }
                }
            })
        }

        //选择风研成果
        window.selectRisk = function(){
            layer.open({
                type: 2,
                id: 'userCheckedModal',
                title: '风研成果',
                content: 'views/audit/bigdata/auditbulletin/components/addRisk.html?project_id='+project_id,
                area: ['95%', '95%'],
                resize: false,
                end: function () {
                }
            });
        }

        //组内权重 值打分
        window.weightListFun = function() {
            var totalItems = riskList.length;
            if (totalItems === 0) {
                weightList = []; // 清空权重数组
                lp(tpl, weightList, $("#group-weight-tpl").html(), $("#weightList")); // 更新界面
                return;
            }

            var baseScore = Math.floor(100 / totalItems); // 每个项的基本分数

            weightList = [];
            for (var i = 0; i < totalItems; i++) {
                weightList.push({
                    researchInfoId:riskList[i].researchInfoId,
                    achievementCode: riskList[i].achievementCode,
                    weight: baseScore
                });
            }

            lp(tpl, weightList, $("#group-weight-tpl").html(), $("#weightList")); // 更新界面
        };

        //用户只能输入正数与小数点两位小数，并在0到100之间
        window.upperCase = function (obj) {
            var value = obj.value;
            //移除多余的负号
            value = value.replace(/-/g, '');
            //确保输入的是数字或小数点
            value = value.replace(/[^0-9.]/g, '');
            //确保只有一个小数点
            if (value.split('.').length > 2) {
                value = value.split('.')[0] + '.' + value.split('.')[1];
            }
            // 确保小数点后最多两位
            if (value.includes('.')) {
                var parts = value.split('.');
                if (parts[1].length > 2) {
                    value = parts[0] + '.' + parts[1].slice(0, 2);
                }
            }
            obj.value = value;
        }
        $("#weightList").on('blur', '.weight-li-input', function () {
            var that = $(this);
            var code = that.attr('code');
            var value = that.val();
            if (0 <= parseFloat(value) && parseFloat(value) <= 100 && value !== '') {
                for (var i = 0; i < weightList.length; i++) {
                    if (weightList[i].researchInfoId == code) {
                        weightList[i].weight = parseFloat(value)
                        that.val(parseFloat(value))
                    }
                }
            } else if (value == '') {
                for (var i = 0; i < weightList.length; i++) {
                    if (weightList[i].researchInfoId == code) {
                        weightList[i].weight = value
                    }
                }
            } else {
                frm.validate("请填写【0~100】之间的值");
                that.val('');
                for (var i = 0; i < weightList.length; i++) {
                    if (weightList[i].researchInfoId == code) {
                        weightList[i].weight = ''
                    }
                }
            }
        });

        // BULLETIN_STATUS 加載
        function initTaskStatus(type, data) {
            typeDictList = data;
        }
        getDicList('DATA_BOARD_SPAN_TYPE', initTaskStatus);

        //删除关联成果
        window.batchDelRiskApplyInfo  = function () {
            //只取checkedRows中的id
            var ids = [];
            for(var i=0;i<riskList.length;i++){
                ids.push(riskList[i].researchInfoId);
            }
            $.ajax({
                url: ctx + "/pro/auditbulletin/batchDelProjectApplyInfo",
                dataType: "JSON",
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                data: JSON.stringify({
                    "projectId": project_id,
                    "researchInfoIds":ids
                }),
                success: function (res) {
                    if (res.httpCode == 200) {
                        weightList = [];
                        riskTableFun()
                    }
                }
            })
        }


    });

    //流程提交环节和人所需变量
    function loadProcessData() {
        return {
            key: 1
        };
    }

    function refreshAssigneeUrl() {
        return "projectRemote/flow";

    }

    //流程推动或退回
    function modal_pass(root, taskDefinitionKey, assignee, processInstanceId, taskId, comment, handleType, withdraw, $, index, layer, sendMsg) {
        layui.use('form', function () {
            var $ = layui.jquery;
            var frm = layui.jqfrm;
        });
        $.post(root + "/projectRemote/flow/pushProcess", {
            "processInstanceId": processInstanceId,
            "taskId": taskId,
            "taskDefinitionKey": taskDefinitionKey,
            "assignee": assignee,
            "comment": comment,
            "handleType": handleType,
            "withdraw": withdraw,
            "sendMsg": sendMsg,
            "projectId":project_id,
            "startupStatus":'1'
        }, function (data) {
            // 成功后回调模态窗口关闭方法
            if (data.httpCode == '200') {
                layer.close(index);
                layer.msg("流程推进成功", {icon: 1, zIndex: 19891015});
                closeIframe($);
            } else {
                layer.close(index);
                layer.confirm('流程推进失败',
                    {
                        icon: 2,
                        title: "失败",
                        skin: 'demo-class',
                        btn: ['确定'],
                        yes: function (errorIndex) {
                            closeIframe($);
                            layer.close(errorIndex);
                        },
                        cancel: function (errorIndex) {
                            closeIframe($);
                            layer.close(errorIndex);
                        }
                    });
            }
        }, 'json');
    }

    //流程推进后调用方法
    function closeIframe($) {
        setInterval(function () {
            parent.toTaskToDoList($);
        }, 3000);
    }

    //初次加载页面加将自定义方法放入总方法的数组中用于统一调用
    layui.use(['jqfrm', 'jqbind', 'laydate'], function () {
        // project_todo.pushFunc(monitoringCenter);

    });

    function monitoringCenter(pDKey, tDKey, root, pInsId, taskId, handleType, link_1, $, index, layer, form) {
        project_todo.refreshLink($, index, layer, form);
        $("#linkDiv").show();
        $("#assigneeDiv").show();
        project_todo.openLink('450px', layer, $);
        // 刷新人员，提交
        project_todo.refreshAssignee(pDKey, $("#link").val().split(",")[0], $, layer, root, form);

    }

    var passStatus = true;
    var judgeClickBtn = false;

    function passValidate() {
        var flag = false;
        var isAssociation = $('input:radio[name="isAssociation"]:checked').val();//是否关联风研成果
        if (!isAssociation) {
            layer.alert('请选择【是否关联风研成果】！', { icon: 2, title: '提示' });
            return false;
        }
        if (isAssociation=='1'&&weightList.length==0) {
            layer.alert('请选择【对应风研成果】！', { icon: 2, title: '提示' });
            return false;
        }
        if (isAssociation=='1'&&weightList.length>0) {
            var num = 0;
            for(var a = 0;a<weightList.length;a++){
                num += weightList[a].weight;
            }
            if(num>100){
                layer.alert('【对应风研成果】权重之和不能大于100！', { icon: 2, title: '提示' });
                return false;
            }
        }
        $.ajax({
            url: ctx + "/pro/auditbulletin/saveProjectApplyInfoWeight",
            "async" : true, // 同步
            dataType: "JSON",
            type: "POST",
            contentType: "application/json;charset=UTF-8",
            data: JSON.stringify({
                "projectId": project_id,
                weightList:weightList
            }),
            success: function (res) {
                if (res.httpCode == 200) {

                }else {
                    frm.error(res.msg);
                    return false;
                }
            }
        });

        layui.use(['jqfrm', 'jqbind', 'laydate', 'jquery', 'table'], function () {
            passStatus = false;
            var $ = layui.jquery,
                table = layui.table;
            $('#saveGroupMembers').click();

            if (passStatus) {
                judgeClickBtn = true;
                flag = true;
            }
        });
        //flag = window.getPassVide();
        return flag;
    }

</script>
</body>

</html>
