<!--填报整体追责-->
<!--#include virtual ="include/header.html"-->
<style>
    .layui-table tr td .layui-table-cell.laytable-cell-2-accountabilityFlag,
    .layui-table tr td .layui-table-cell.laytable-cell-2-punishLevel,
    .layui-table tr td .layui-table-cell.laytable-cell-2-blackFlag {
        overflow: visible;
    }

    .layui-table tr [data-field="accountabilityFlag"] .layui-table-cell,
    .layui-table tr [data-field="punishLevel"] .layui-table-cell,
    .layui-table tr [data-field="blackFlag"] .layui-table-cell {
        overflow: visible;
    }

    .new-style .layui-form-item.layui-form-item-sm .layui-unselect {
        height: 26px !important;
    }

    .layui-form-select dl dd {
        line-height: 22px;
    }

    .layui-form-selected dl {
        padding: 0px;
    }

    .layui-table-cell {
        height: auto;
        padding: 0 8px;
        line-height: 20px;
        position: relative;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        box-sizing: border-box;
    }

    .layui-form-pane .layui-form-label {
        background: transparent;
    }

    .form-conmon-more .layui-form-item {
        margin-bottom: 12px;
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 20px 0 15px 0;
    }

    .section-title {
        display: flex;
        align-items: center;
        margin: 0;
        font-size: 16px;
        font-weight: bold;
        color: #333;
    }

    .section-title::before {
        content: '';
        width: 4px;
        height: 18px;
        background-color: #ff4444;
        margin-right: 8px;
        border-radius: 2px;
    }

    .section-buttons {
        display: flex;
        gap: 8px;
    }

    /* 生成整体追责按钮特殊样式 */
    .btn-generate-accountability {
        background-color: #c20000 !important;
        border-color: #c20000 !important;
        color: #fff !important;
    }

    .btn-generate-accountability:hover {
        background-color: #c20000 !important;
        border-color: #c20000 !important;
    }

    .btn-generate-accountability:active {
        background-color: #c20000 !important;
        border-color: #c20000 !important;
    }

    /* 禁用状态的生成整体追责按钮 */
    .btn-generate-accountability.layui-btn-disabled {
        background-color: #c2c2c2 !important;
        border-color: #c2c2c2 !important;
        color: #fff !important;
        opacity: 0.6;
        cursor: not-allowed !important;
    }

    .btn-generate-accountability.layui-btn-disabled:hover {
        background-color: #c2c2c2 !important;
        border-color: #c2c2c2 !important;
    }

    /* 上传附件按钮样式 */
    #uploadAttachmentBtnMain.layui-btn-disabled {
        opacity: 0.6;
        cursor: not-allowed !important;
        background-color: #c2c2c2 !important;
        border-color: #c2c2c2 !important;
        color: #fff!important;
    }

    #uploadAttachmentBtnMain.layui-btn-disabled:hover {
        background-color: #c2c2c2 !important;
        border-color: #c2c2c2 !important;
         color: #fff!important;
    }

    /* 附件类型选择框样式 */
    #attachmentType {
        transition: border-color 0.3s ease;
    }

    #attachmentType:focus {
        border-color: #1e9fff !important;
        outline: none;
    }

    /* 附件操作按钮样式 */
    .table-btn {
        margin-right: 8px;
        padding: 4px 8px;
        border-radius: 3px;
        transition: all 0.3s ease;
    }

    .table-btn:hover {
        background-color: #f5f5f5;
    }

    .table-btn:last-child {
        margin-right: 0;
    }

    /* 附件表格样式优化 */
    #attachmentTable .layui-table-cell {
        height: auto;
        line-height: 1.4;
    }

    /* 批量删除附件按钮样式 */
    #batchDeleteAttachmentBtn.layui-btn-disabled {
        opacity: 0.6;
        cursor: not-allowed !important;
        background-color: #c2c2c2 !important;
        border-color: #c2c2c2 !important;
          color: #fff!important;
    }

    .layui-form-pane .layui-form-label{
        width: 120px;
    }
.layui-form-pane .layui-input-block{
    margin-left: 120px;
}
    #batchDeleteAttachmentBtn.layui-btn-disabled:hover {
        background-color: #c2c2c2 !important;
        border-color: #c2c2c2 !important;
        color: #fff!important;
    }

    /* 必填字段红星样式 */
    .layui-form-label i[style*="color: #c20000"] {
        font-weight: bold;
        font-size: 12px;
        line-height: 1;
        padding-right:2px;
    }

    /* 必填字段标签样式优化 */
    .layui-form-label {
        position: relative;
        padding-left: 8px;
    }

    /* 附件类型选择框样式 */
    #attachmentType {
        transition: border-color 0.3s ease;
    }

    #attachmentType:focus {
        border-color: #1e9fff !important;
        box-shadow: 0 0 0 2px rgba(30, 159, 255, 0.2);
    }

    /* 附件类型未选择时的提示样式 */
    .attachment-type-required {
        border-color: #ff5722 !important;
        box-shadow: 0 0 0 2px rgba(255, 87, 34, 0.2);
        animation: shake 0.5s ease-in-out;
    }

    /* 基本信息字段未填写时的提示样式 */
    .field-required {
        border-color: #ff5722 !important;
        box-shadow: 0 0 0 2px rgba(255, 87, 34, 0.2);
        animation: shake 0.5s ease-in-out;
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }

    /* 项目筛选按钮样式 */
    #projectFilterBtn {
        border-color: #c20000 !important;
        color: #c20000 !important;
    }

    #projectFilterBtn:hover {
        background-color: #c20000 !important;
        border-color: #c20000 !important;
         color: #fff !important;
    }

    #projectFilterBtn:active {
        background-color: #c20000 !important;
        border-color: #c20000 !important;
            color: #fff !important;
    }

    /* 禁用状态的项目筛选按钮 */
    #projectFilterBtn.layui-btn-disabled {
        background-color: #c2c2c2 !important;
        border-color: #c2c2c2 !important;
        color: #fff !important;
        opacity: 0.6;
        cursor: not-allowed !important;
    }

    #projectFilterBtn.layui-btn-disabled:hover {
        background-color: #c2c2c2 !important;
        border-color: #c2c2c2 !important;
    }

    /* 追责中状态的只读字段样式 */
    .readonly-field {
        cursor: not-allowed !important;
    }

    /* 只读状态的复选框样式 */
    input[type="checkbox"].readonly-field {
        pointer-events: none !important;
        opacity: 0.6 !important;
    }

    /* 只读状态的复选框开关样式 */
    .readonly-field + .layui-form-switch {
        pointer-events: none !important;
    }

    /* 状态提示样式 */
    .status-notice {
        font-size: 14px;
        line-height: 1.5;
    }

    .status-notice i {
        margin-right: 8px;
    }
</style>

<body>
    <div class="layui-fluid larry-wrapper">
        <section class="panel panel-padding">
            <div class="form-bg-box" style="
    background: #f5f7fa;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    border: 1px solid #e4e7ed;
    padding: 24px 16px 8px 16px;
    margin-bottom: 16px;">
                <form class="layui-form layui-form-pane form-conmon form-conmon-more" data-params='{bind:true }'
                    id="fillForm">
                    <input type="hidden" id="accountabilityId" name="accountabilityId" />
                    <div class="layui-row layui-col-space10 transition-500ms">
                        <div class="layui-col-md4 layui-col-sm4">
                            <div class="layui-form-item layui-form-item-sm">
                                <label class="layui-form-label"><i style="color: #c20000;">*</i>立项单位</label>
                                <div class="layui-input-block">
                                    <input class="layui-input" id="sendProvName" name="sendProvName" value="集团"
                                        type="text" readonly />
                                    <input type="hidden" id="sendProvCode" name="sendProvCode" />
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4 layui-col-sm4">
                            <div class="layui-form-item layui-form-item-sm">
                                <label class="layui-form-label"><i style="color: #c20000;">*</i>再审计期间</label>
                                <div class="layui-input-block">
                                    <input class="layui-input" id="checkMonth" name="checkMonth" placeholder="再审计期间"
                                        readonly="readonly">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4 layui-col-sm4">
                            <div class="layui-form-item layui-form-item-sm">
                                <label class="layui-form-label"><i style="color: #c20000;">*</i>再审计名称</label>
                                <div class="layui-input-block">
                                    <input class="layui-input" id="checkAuditName" name="checkAuditName"
                                        placeholder="请输入再审计名称" type="text" />
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4 layui-col-sm4">
                            <div class="layui-form-item layui-form-item-sm">
                                <label class="layui-form-label"><i style="color: #c20000;">*</i>到日</label>
                                <div class="layui-input-block">
                                    <input class="layui-input" id="auditPeriod" name="auditPeriod" placeholder="审计时间区间"
                                        readonly="readonly">
                                </div>
                            </div>
                        </div>

                        <div class="layui-col-md4 layui-col-sm4" id = "isSubCheckDiv" style="display: none;">
                            <div class="layui-form-item layui-form-item-sm">
                                <label class="layui-form-label"><i style="color: #c20000;">*</i>是否对下检查</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" id="isSubCheck" name="isSubCheck" lay-skin="switch"
                                        lay-text="是|否" lay-filter="isSubCheck" />
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4 layui-col-sm4">
                            <div class="layui-form-item layui-form-item-sm" style="display:flex;align-items:center;">
                                <label class="layui-form-label"><i style="color: #c20000;">*</i>再审计小组成员</label>
                                <div class="layui-input-inline" style="flex:1;">
                                    <input class="layui-input" id="auditTeam" name="auditTeam" placeholder="请输入成员"
                                        type="text" />
                                </div>
                                <button class="layui-btn layui-btn-sm" type="button" onclick="queryUser()"
                                    style="margin-left:8px;">选择</button>
                            </div>
                        </div>
                        <div class="layui-col-md4 layui-col-sm4">
                            <div class="layui-form-item layui-form-item-sm">
                                <label class="layui-form-label">组织方式</label>
                                <div class="layui-input-block">
                                    <input class="layui-input" id="auditTypeName" name="auditTypeName" type="text"
                                        readonly />
                                    <input type="hidden" id="auditType" name="auditType" />
                                </div>
                            </div>
                        </div>
                        <!-- 整改状态字段，只在追责中状态时显示 -->
                        <div class="layui-col-md4 layui-col-sm4" id="reformStatusDiv" style="display: none;">
                            <div class="layui-form-item layui-form-item-sm">
                                <label class="layui-form-label">整改状态</label>
                                <div class="layui-input-block">
                                    <select id="reformStatus" name="reformStatus" lay-filter="reformStatus">
                                        <!-- 通过模板渲染 -->
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="layui-form" id="checkProjectSection" style="margin-top:10px; display: none;">
                <div class="section-header">
                    <div class="section-title">抽查项目列表</div>
                    <div class="section-buttons">
                        <button class="layui-btn layui-btn-sm layui-btn-normal" id="projectFilterBtn" onclick="projectFilter()">
                            <i class="iconfont search-icon">&#xe625;</i> 项目筛选
                        </button>
                        <button class="layui-btn layui-btn-sm" onclick="exportProject()">
                            <i class="iconfont search-icon">&#xe61d; </i> 导出抽查项目
                        </button>

                        <button class="layui-btn layui-btn-sm" onclick="importProject()">
                            <i class="iconfont search-icon">&#xe60c;</i> 导入抽查项目
                        </button>


                        <button style="display: none;" id="uploadProjectBtn"></button>

                                <button class="layui-btn layui-btn-sm layui-btn-danger" id="batchDeleteBtn" onclick="batchDeleteItems()" disabled>
                            <i class="iconfont search-icon">&#xe7f3;</i> 批量删除
                        </button>
                    </div>
                </div>
                <table class="layui-table jq-even" id="checkLedgersTable" lay-filter="checkLedgersTable"></table>
            </div>

            <div class="layui-form" style="margin-top:10px">
                <div class="section-header">
                    <div class="section-title">项目台账</div>
                    <div class="section-buttons">
                        <button class="layui-btn layui-btn-sm btn-generate-accountability"
                            onclick="createOverallAccountabilityInfo()">
                            <i class="iconfont search-icon">&#xe80d;</i> 生成整体追责
                        </button>

                    </div>
                </div>

                <table class="layui-table jq-even" id="accountabilityTable" lay-filter="accountabilityTable"></table>

                <div class="layui-form" style="margin-top:20px">
                    <div class="section-header">
                        <div class="section-title">附件列表</div>
                        <div class="section-buttons" style="display: flex; align-items: center; gap: 10px;">
                            <div class="layui-form" style="display: flex; align-items: center; gap: 8px;">
                                <label style="font-size: 14px; color: #333;">附件类型：</label>
                                <select id="attachmentType"
                                    style="width: 150px; height: 32px; border: 1px solid #e6e6e6; border-radius: 2px; padding: 0 10px;">
                                    <option value="">--请选择附件类型--</option>
                                    <option value="1">审计报告</option>
                                    <option value="2">审计底稿</option>
                                    <option value="3">相关证明材料</option>
                                    <option value="4">整改报告</option>
                                    <option value="5">其他材料</option>
                                </select>



                            </div>
                            <button class="layui-btn layui-btn-sm" id="uploadAttachmentBtnMain"
                                onclick="uploadAttachment()">
                                <i class="iconfont search-icon">&#xe61d;</i> 上传附件
                            </button>
                            <!-- <button class="layui-btn layui-btn-sm" id="batchDeleteAttachmentBtn"
                                onclick="batchDeleteAttachments()">
                                <i class="iconfont search-icon">&#xe640;</i> 批量删除
                            </button> -->
                        </div>
                    </div>
                    <table class="layui-table jq-even" id="attachmentTable" lay-filter="attachmentTable"></table>
                    <button style="display: none;" id="uploadAttachmentBtn"></button>
                </div>
        </section>
    </div>
</body>
<!--#include virtual ="include/version.html"-->

<!--操作列模板-->
<script id="configBar" type="text/html">
    <a class="table-btn" type="button" title="删除" onclick="deleteSingleItem('{{d.endAccountAttrId}}', '{{d.projectName}}')"
       style="margin: 0 2px; padding: 4px 8px; border-radius: 3px; transition: all 0.2s ease; display: inline-block; text-decoration: none; cursor: pointer;">
        <i class="iconfont" style="color: #c20000;">&#xe7f3;</i>
    </a>
</script>

<script id="accountabilityFlag" type="text/html">
    <div class="new-style">
        <div class="layui-form-item layui-form-item-sm">
            <select  name="accountabilityFlag" lay-filter="accountabilityFlag">
                <option lay-event="accountabilityFlag" value="" {{d.accountabilityFlag==''?'selected':''}}>--请选择--</option>
                <option lay-event="accountabilityFlag" value="1" {{d.accountabilityFlag=='1'?'selected':''}}>是</option>
                <option lay-event="accountabilityFlag" value="0" {{d.accountabilityFlag=='0'?'selected':''}}>否</option>
            </select>
        </div>
    </div>
</script>

<script id="punishLevel" type="text/html">
    <div class="new-style">
        <div class="layui-form-item layui-form-item-sm">
            <select  name="punishLevel" lay-filter="punishLevel">
                <option lay-event="punishLevel" value="" {{d.punishLevel==''?'selected':''}}>--请选择--</option>
                <option lay-event="punishLevel" value="0" {{d.punishLevel=='0'?'selected':''}}>一般</option>
                <option lay-event="punishLevel" value="1" {{d.punishLevel=='1'?'selected':''}}>严重</option>
                <option lay-event="punishLevel" value="2" {{d.punishLevel=='2'?'selected':''}}>重大</option>
            </select>
        </div>
    </div>
</script>

<script id="blackFlag" type="text/html">
    <div class="new-style">
        <div class="layui-form-item layui-form-item-sm">
            <select  name="blackFlag" lay-filter="blackFlag">
                <option lay-event="blackFlag" value="" {{d.blackFlag==''?'selected':''}}>--请选择--</option>
                <option lay-event="blackFlag" value="1" {{d.blackFlag=='1'?'selected':''}}>是</option>
                <option lay-event="blackFlag" value="0" {{d.blackFlag=='0'?'selected':''}}>否</option>
            </select>
        </div>
    </div>
</script>

<!--附件操作模板-->
<script id="attachmentBar" type="text/html">
    <a class="table-btn" type="button" title="下载" onclick="downloadAttachment('{{d.attachmentId}}', '{{d.fileName}}')">
        <i class="iconfont" style="color: #c20000;">&#xe61d;</i>
    </a>
    {{# if(d.submitFlag != '1' && d.submitFlag != 1) { }}
    <a class="table-btn" type="button" title="删除" onclick="deleteAttachment('{{d.id}}', '{{d.fileName}}')">
        <i class="iconfont" style="color: #c20000;">&#xe7f3;</i>
    </a>
    {{# } else { }}
    <a class="table-btn" type="button" title="已提交，不可删除" style="opacity: 0.5; cursor: not-allowed; pointer-events: none;">
        <i class="iconfont" style="color: #ccc;">&#xe7f3;</i>
    </a>
    {{# } }}
</script>

<!--项目台账查看操作模板-->
<script id="accountabilityViewBar" type="text/html">
    <a class="table-btn" type="button" title="查看" onclick="showCheckLedgersList('{{d.id}}')">
        <i class="iconfont">&#xe6b6;</i>
    </a>
</script>

<!--抽查项目查看操作模板-->
<script id="checkLedgersViewBar" type="text/html">
    <a class="table-btn" type="button" title="查看" onclick="showCheckLedgersDetail('{{d.endAccountAttrId}}')">
        <i class="iconfont">&#xe6b6;</i>
    </a>
</script>

<!--整改状态选项模板-->
<script id="reformStatusTemplate" type="text/html">
    <option value="">--请选择整改状态--</option>
    {{# layui.each(d, function(index, item){ }}
        <option value="{{item.code}}">{{item.codeText}}</option>
    {{# }); }}
</script>
<script type="text/javascript">
    layui.use(['jqdate', 'jqform', 'jqfrm', 'jqbind', 'tableNew', 'layer', 'jquery', 'upload'], function () {
        var $ = layui.jquery;
        var frm = layui.jqfrm;
        var form = layui.jqform;
        var jqbind = layui.jqbind;
        var layer = layui.layer;
        var table = layui.tableNew;
        var ctx = top.global.ctx;
        var laydate = layui.laydate;
        var tpl = layui.laytpl;
        var element = layui.element;
        var upload = layui.upload;

        // 追责项目表格数据缓存
        var accountabilityTableData = [];



        //主表主键
        var accountabilityId = getUrlParam("accountabilityId");
        //追责状态
        var accountabilityStatus = getUrlParam("accountabilityStatus");

        // 查询追责信息
        window.queryAccountabilityInfo = function () {
            var indexZG = layer.load(1, {
                shade: [0.4, '#fff']
            });

            // 判断是新增还是编辑
            if (!accountabilityId || accountabilityId == '' || accountabilityId == 'undefined') {
                // 新增模式：调用新增接口获取基本信息并生成追责主键
                queryAddAccountabilityInfo(indexZG);
            } else {
                // 编辑模式：调用原有接口
                queryExistingAccountabilityInfo(indexZG);
            }
        }

        // 新增时查询基本信息并生成追责主键
        window.queryAddAccountabilityInfo = function (indexZG) {
            $.ajax({
                url: ctx + "/prj/overallAccountabilitynew/queryAddAccountabilityInfo",
                type: "POST",
                dataType: "json",
                data: JSON.stringify({}),
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    if (200 == ret.httpCode) {
                        var data = ret.data;

                        // 设置生成的追责主键
                        if (data.accountabilityId) {
                            accountabilityId = data.accountabilityId;
                        }

                        // 处理立项单位：默认显示当前登录人所在省分，集团默认显示"集团"，不可修改
                        if (data.sendProvCode && data.sendProvName) {
                            $("#sendProvCode").val(data.sendProvCode);
                            $("#sendProvName").val(data.sendProvName);
                        }


                        // 处理日期字段格式化
                        if (data.selectStartMonth && data.selectEndMonth) {
                            $("#checkMonth").val(data.selectStartMonth + " - " + data.selectEndMonth);
                        }
                        if (data.auditDateStart && data.auditDateEnd) {
                            $("#auditPeriod").val(data.auditDateStart + " - " + data.auditDateEnd);
                        }

                        if( data.showBelow == '1'){
                            $("#isSubCheckDiv").show();
                            // 处理复选框
                            if (data.checkBelow == '1') {
                                $("#isSubCheck").prop("checked", true);
                            }
                        }else{
                            $("#isSubCheckDiv").hide();
                        }

                        // 处理组织方式回显
                        if (data.auditType) {
                            setAuditTypeName(data.auditType);
                        }

                        // 重新渲染表单
                        form.render();

                        // 根据是否对下检查状态控制抽查项目列表显示
                        queryCheckLedgersTableInfo();
                        // 加载项目台账数据
                        queryAccountabilityTable();

                        //加载附件列表
                        queryAttachmentTable();

                        layer.close(indexZG);
                    } else {
                        layer.close(indexZG);
                        frm.error(ret.msg || "获取基本信息失败");
                    }
                },
                error: function (e) {
                    layer.close(indexZG);
                    frm.error("网络异常，获取基本信息失败");
                }
            });
        }

        // 编辑时查询已有追责信息并判断权限
        window.queryExistingAccountabilityInfo = function (indexZG) {
            $.ajax({
                url: ctx + "/prj/overallAccountabilitynew/queryEditAccountabilityInfo",
                type: "POST",
                dataType: "json",
                data: JSON.stringify({ accountabilityId: accountabilityId }),
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    if (200 == ret.httpCode) {
                        var data = ret.data;

                        // 检查是否有权限错误信息
                        if (data.errorMsg) {
                            layer.close(indexZG);
                            frm.error(data.errorMsg);
                            return;
                        }

                        // 处理立项单位：显示接口返回的省分信息
                        if (data.sendProvCode && data.sendProvName) {
                            $("#sendProvCode").val(data.sendProvCode);
                            $("#sendProvName").val(data.sendProvName);
                        }



                        // 处理日期字段格式化
                        if (data.selectStartMonth && data.selectEndMonth) {
                            $("#checkMonth").val(data.selectStartMonth + " - " + data.selectEndMonth);
                        }
                        if (data.auditDateStart && data.auditDateEnd) {
                            $("#auditPeriod").val(data.auditDateStart + " - " + data.auditDateEnd);
                        }

                        if( data.showBelow == '1'){
                            $("#isSubCheckDiv").show();
                            // 处理复选框
                            if (data.checkBelow == '1') {
                                $("#isSubCheck").prop("checked", true);
                            }
                        }else{
                            $("#isSubCheckDiv").hide();
                        }



                        //再审计名称
                        $("#checkAuditName").val(data.checkAuditName);
                        //再审计小组成员
                        $("#auditTeam").val(data.auditUserNames);

                        // 处理组织方式回显
                        if (data.auditType) {
                            setAuditTypeName(data.auditType);
                        }

                        // 处理整改状态回显（需要在字典加载完成后设置）
                        if (data.reformStatus !== undefined && data.reformStatus !== null) {
                            // 延迟设置，确保字典数据已加载
                            setTimeout(function() {
                                $("#reformStatus").val(data.reformStatus);
                                form.render('select');
                            }, 100);
                        }

                        //判断是否可以编辑整改状态
                        if(data.editReformStatus == '0'){
                            $("#reformStatus").attr("disabled", true);
                        }else{
                            $("#reformStatus").removeAttr("disabled", true);
                        }

                        // 重新渲染表单
                        form.render();

                        // 根据是否对下检查状态控制抽查项目列表显示
                        queryCheckLedgersTableInfo();

                        // 加载项目台账数据
                        queryAccountabilityTable();

                        //加载附件列表
                        queryAttachmentTable();

                        layer.close(indexZG);
                    } else {
                        layer.close(indexZG);
                        frm.error(ret.msg || "获取追责信息失败");
                    }
                },
                error: function (e) {
                    layer.close(indexZG);
                    frm.error("网络异常，获取追责信息失败");
                }
            });
        }



        window.queryAccountabilityTable = function (data) {
            // 如果传入了数据，直接渲染（用于生成整体追责、保存等操作）
            if (data && data.length > 0) {
                renderAccountabilityTable(data);
                return;
            }

            // 否则从接口获取数据
            var indexZG = layer.load(1, {
                shade: [0.4, '#fff']
            });

            // 获取再审计期间参数
            var checkMonth = $("#checkMonth").val();
            var checkBelow = $("#isSubCheck").prop("checked") ? "1" : "0";
            var params = {
                accountabilityId: accountabilityId,
                checkBelow: $("#isSubCheck").prop("checked") ? "1" : "0"
            };

            if (checkMonth) {
                params.selectStartMonth = checkMonth.substr(0, 6);
                params.selectEndMonth = checkMonth.substr(9);
            }
            var url = "/prj/overallAccountabilitynew/queryAccountabilityDetailList";
            if(!accountabilityStatus && checkBelow !== "1"){
                //追责状态为未发起并且是不对下检查，走原先的整体追责初始化方法
                url = "/prj/overallAccountability/createOverallAccountabilityInfo";
            }

            $.ajax({
                url: ctx + url,
                type: "POST",
                dataType: "json",
                data: JSON.stringify(params),
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    layer.close(indexZG);
                    if (ret.httpCode == 200 && ret.data) {
                        renderAccountabilityTable(ret.data);
                    } else {
                        renderAccountabilityTable([]);
                    }
                },
                error: function (e) {
                    layer.close(indexZG);
                    renderAccountabilityTable([]);
                    frm.error("网络异常，获取项目台账数据失败");
                }
            });
        };

        // 渲染项目台账表格
        window.renderAccountabilityTable = function (data) {
            table.render({
                elem: '#accountabilityTable',
                id: 'accountabilityTable',
                data: data || [],
                height: 'full-300',
                page: false,
                even: true, //开启隔行背景
                cols: [[
                    { type: 'numbers', title: '序号', width: 50, align: 'center', rowspan: 3 },
                    { title: '整体处罚', width: 760, align: 'center', style: 'text-align: center', colspan: 6 },
                    { field: 'sendProvName', title: '报审省分', width: 120, align: 'center', style: 'text-align: center', rowspan: 3 },
                    { field: 'checkAuditName', title: '再审计名称', width: 200, align: 'center', style: 'text-align: left', rowspan: 3 },
                    { field: 'checkStartEndMonth', title: '再审计期间', width: 150, align: 'center', style: 'text-align: center', rowspan: 3 ,templet :function(d){
                            if(!d.checkStartEndMonth){
                                return d.checkStartMonth + "-" + d.checkEndMonth;
                            }else{
                                return d.checkStartEndMonth;
                            }
                        }
                    },
                    { field: 'reformStatus', title: '整改状态', width: 100, align: 'center', style: 'text-align: center', rowspan: 3 ,templet :function(d){
                            if(d.reformStatus == '0'){
                                return '未整改'
                            }else if(d.reformStatus == '1'){
                                return '整改中'
                            }else if(d.reformStatus == '2'){
                                return '整改完成'
                            }else{
                                return '';
                            }
                        }},
                    { field: 'agencyName', title: '原审计机构名称', width: 300, align: 'center', style: 'text-align: left', rowspan: 3 },
                    { field: 'checkProNum', title: '再审计<br />项目数量', width: 100, align: 'center', style: 'text-align: center', rowspan: 3 },
                    { field: 'professionOneName', title: '涉及A级专业', width: 140, align: 'center', style: 'text-align: left', rowspan: 3 },
                    { title: '项目送审金额合计(元)', width: 720, align: 'center', style: 'text-align: center', colspan: 6 },
                    { title: '初次审减金额合计（元）', width: 720, align: 'center', style: 'text-align: center', colspan: 6 },
                    { title: '再审计审减金额合计(元)', width: 720, align: 'center', style: 'text-align: center', colspan: 6 },
                    { title: '整体评价标准', width: 420, align: 'center', style: 'text-align: center', colspan: 3 },
                ], [
                    { field: 'accountabilityFlag', title: '是否追责', toolbar: '#accountabilityFlag', width: 120, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'punishLevel', title: '处罚档次', toolbar: '#punishLevel', width: 120, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'payScaleInterval', title: '赔偿全部同类项目<br>审计费比例（%）', edit: 'text', width: 140, align: 'center', style: 'text-align: center;border: 1px dashed #f5222d', rowspan: 2 },
                    { title: '处罚措施', width: 380, align: 'center', style: 'text-align: center', colspan: 3 },
                    {
                        field: 'sendAmountExecutionCost', title: '施工费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.sendAmountExecutionCost);
                        }
                    },
                    {
                        field: 'sendAmountMaterialsCostB', title: '其中:乙供材料费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.sendAmountMaterialsCostB);
                        }
                    },
                    {
                        field: 'sendAmountMaterialsCostA', title: '甲供材料费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.sendAmountMaterialsCostA);
                        }
                    },
                    {
                        field: 'sendAmountEquit', title: '甲供设备费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.sendAmountEquit);
                        }
                    },
                    {
                        field: 'sendAmountOther', title: '其它', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.sendAmountOther);
                        }
                    },
                    {
                        field: 'sendAmount', title: '合计', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.sendAmount);
                        }
                    },
                    {
                        field: 'cutAmountExecutionCost', title: '施工费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.cutAmountExecutionCost);
                        }
                    },
                    {
                        field: 'cutAmountMaterialsCostB', title: '其中:乙供材料费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.cutAmountMaterialsCostB);
                        }
                    },
                    {
                        field: 'cutAmountMaterialsCostA', title: '甲供材料费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.cutAmountMaterialsCostA);
                        }
                    },
                    {
                        field: 'cutAmountEquit', title: '甲供设备费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.cutAmountEquit);
                        }
                    },
                    {
                        field: 'cutAmountOther', title: '其它', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.cutAmountOther);
                        }
                    },
                    {
                        field: 'cutAmount', title: '合计', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.cutAmount);
                        }
                    },
                    {
                        field: 'checkCutAmountExecutionCost', title: '施工费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.checkCutAmountExecutionCost);
                        }
                    },
                    {
                        field: 'checkCutAmountMaterialsCostB', title: '其中:乙供材料费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.checkCutAmountMaterialsCostB);
                        }
                    },
                    {
                        field: 'checkCutAmountMaterialsCostA', title: '甲供材料费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.checkCutAmountMaterialsCostA);
                        }
                    },
                    {
                        field: 'checkCutAmountEquit', title: '甲供设备费', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.checkCutAmountEquit);
                        }
                    },
                    {
                        field: 'checkCutAmountOther', title: '其它', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.checkCutAmountOther);
                        }
                    },
                    {
                        field: 'checkCutAmount', title: '合计', width: 120, align: 'center', style: 'text-align: right', rowspan: 2, templet: function (d) {
                            return formatNumContainNull(d.checkCutAmount);
                        }
                    },
                    { field: 'averageCheckCutRate', title: '全部抽查项目<br>平均再审减率', width: 140, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'checkCutAmountExecutionCostAverage', title: '本省同类工程<br>施工费平均审减率', width: 140, align: 'center', style: 'text-align: center', rowspan: 2 },
                    { field: 'averageDeviationDegree', title: '全部抽查项目<br>平均再审减偏离度', width: 140, align: 'center', style: 'text-align: center', rowspan: 2 },
                ], [
                    { field: 'reduceShare', title: '调减份额', width: 120, edit: 'text', align: 'center', style: 'border: 1px dashed #f5222d' },
                    { field: 'payAuditAmount', title: '或赔偿全部同类<br>项目审计费（元）', width: 140, align: 'center', style: 'text-align: right', },
                    { field: 'blackFlag', title: '终止合作并<br>列入黑名单', toolbar: '#blackFlag', width: 120, align: 'center', style: 'text-align: center' },
                    { title: '操作', width: 80, align: 'center', templet: '#accountabilityViewBar', fixed: 'right' }
                ]
                ],
                done: function (res) {
                    accountabilityTableData = res.data || [];
                }
            });
        }

        /*****************************追责项目表格事件处理***********************************/
        // 是否追责下拉框事件
        form.on('select(accountabilityFlag)', function (data) {
            var elem = data.othis.parents('tr');
            var dataindex = elem.attr("data-index");
            table.cache['accountabilityTable'][dataindex].accountabilityFlag = data.value;
        });

        // 处罚档次下拉框事件
        form.on('select(punishLevel)', function (data) {
            var elem = data.othis.parents('tr');
            var dataindex = elem.attr("data-index");
            table.cache['accountabilityTable'][dataindex].punishLevel = data.value;
        });

        // 黑名单下拉框事件
        form.on('select(blackFlag)', function (data) {
            var elem = data.othis.parents('tr');
            var dataindex = elem.attr("data-index");
            table.cache['accountabilityTable'][dataindex].blackFlag = data.value;
        });

        // 表格编辑事件
        table.on('edit(accountabilityTable)', function (obj) {
            console.log('编辑事件对象:', obj);
            var data = obj.data;
            var text = obj.value;
            var field = obj.field;

            // 获取行索引的多种方法
            var index = -1;

            // 方法1：使用 layui 自动添加的索引
            if (typeof data.LAY_TABLE_INDEX !== 'undefined') {
                index = data.LAY_TABLE_INDEX;
                console.log('使用 LAY_TABLE_INDEX:', index);
            }
            // 方法2：从表格缓存中查找
            else {
                var tableData = table.cache['accountabilityTable'] || accountabilityTableData;
                for (var i = 0; i < tableData.length; i++) {
                    if (tableData[i] === data) {
                        index = i;
                        console.log('从缓存中找到索引:', index);
                        break;
                    }
                }
            }
            // 方法3：使用 jQuery 查找行索引（备用方案）
            if (index === -1 && obj.tr) {
                index = $(obj.tr).index();
                console.log('使用 jQuery 索引:', index);
            }

            if (index === -1) {
                console.error('无法获取行索引');
                return;
            }

            // 数字校验
            var number2Check = /^-?(([0-9])|([1-9]\d*)|(([0-9]\.\d{1,2}|[1-9]\d*\.\d{1,2})))$/;
            var number2CheckMsg = "请填写两位小数！";

            if (text != null && text != '' && !number2Check.test(text)) {
                window.top.layer.alert(number2CheckMsg, {
                    icon: 0
                });
                if (field == 'payScaleInterval') {
                    accountabilityTableData[index][field] = '';
                    accountabilityTableData[index].payAuditAmount = '';
                } else {
                    accountabilityTableData[index][field] = '';
                }
                table.reload('accountabilityTable', {
                    data: accountabilityTableData
                });
                return;
            } else {
                // 如果是赔偿比例字段，需要计算赔偿金额
                    $.ajax({
                        url: ctx + "/prj/overallAccountability/countPayForAuditAmount",
                        type: 'POST',
                        dataType: "JSON",
                        data: JSON.stringify(data),
                        contentType: "application/json;charset=UTF-8",
                        success: function (res) {
                            if (res.httpCode == 200) {
                                // 安全检查：确保 obj.update 方法存在
                                if (typeof obj.update === 'function') {
                                    if (res.data) {
                                        obj.update({
                                            payAuditAmount: res.data
                                        });
                                    } else if (res.data == 0) {
                                        obj.update({
                                            payAuditAmount: 0
                                        });
                                    } else {
                                        obj.update({
                                            payAuditAmount: ''
                                        });
                                    }
                                } else {
                                    console.error('obj.update 方法不存在');
                                }
                            } else {
                                frm.error(res.msg);
                            }
                        }
                    });
            }
        });
        /*****************************追责项目表格事件处理***********************************/

        // 初始化日期选择器
        function initDatePickers() {
            //执行一个laydate实例
            laydate.render({
                elem: '#checkMonth',
                type: 'month',
                range: true,
                format: 'yyyyMM',
                isInitValue: false,
                showBottom: true,
                trigger: accountabilityStatus == '2' ? 'none' : 'click', // 追责中状态禁用触发
                change: function (value, date, endDate) {
                    $("#checkMonth").val(value);
                },
            });

            // 审计时间区间
            laydate.render({
                elem: '#auditPeriod',
                type: 'month',
                range: true,
                format: 'yyyyMM',
                isInitValue: false,
                showBottom: true,
                trigger: accountabilityStatus == '2' ? 'none' : 'click', // 追责中状态禁用触发
                change: function (value, date, endDate) {
                    $("#auditPeriod").val(value);
                },
            });
        }

        // 调用初始化日期选择器
        initDatePickers();

        // 辅助函数：获取所有表单字段值（包括未选中的checkbox和隐藏字段）
        function getAllFormFields() {
            var fields = {};

            // 获取所有input字段
            $('#fillForm input').each(function() {
                var $this = $(this);
                var name = $this.attr('name');
                if (name) {
                    if ($this.attr('type') === 'checkbox') {
                        // checkbox特殊处理
                        fields[name] = $this.prop('checked') ? 'on' : '';
                    } else {
                        fields[name] = $this.val() || '';
                    }
                }
            });

            // 获取所有select字段
            $('#fillForm select').each(function() {
                var $this = $(this);
                var name = $this.attr('name');
                if (name) {
                    fields[name] = $this.val() || '';
                }
            });

            // 获取所有textarea字段
            $('#fillForm textarea').each(function() {
                var $this = $(this);
                var name = $this.attr('name');
                if (name) {
                    fields[name] = $this.val() || '';
                }
            });

            return fields;
        }

        //获取参数
        window.getParams = function () {
            // 使用改进的方法获取所有表单字段
            var params = getAllFormFields();

            // 追责表主键
            params.accountabilityId = accountabilityId;

            // 处理再审计期间 - 生成衍生字段
            var checkMonth = params.checkMonth || $("#checkMonth").val();
            if (checkMonth) {
                params.checkStartMonth = checkMonth.substr(0, 6);
                params.checkEndMonth = checkMonth.substr(9);
                // 同时设置选择账期字段
                params.selectStartMonth = checkMonth.substr(0, 6);
                params.selectEndMonth = checkMonth.substr(9);
            }

            // 处理审计时间区间 - 生成衍生字段
            var auditPeriod = params.auditPeriod || $("#auditPeriod").val();
            if (auditPeriod) {
                params.auditStartMonth = auditPeriod.substr(0, 6);
                params.auditEndMonth = auditPeriod.substr(9);
                // 同时设置审计时间区间字段
                params.auditDateStart = auditPeriod.substr(0, 6);
                params.auditDateEnd = auditPeriod.substr(9);
            }

            // 处理是否对下检查（用于后端处理）
            params.checkBelow = $("#isSubCheck").prop("checked") ? "1" : "0";

            // 设置整体追责状态（从URL参数获取，默认为草稿中）
            params.accountabilityStatus = accountabilityStatus || "0";

            // 处理整改状态（只在追责中状态时有效）
            if (accountabilityStatus == '2') {
                params.reformStatus = params.reformStatus || $("#reformStatus").val() || "";
            }

            // 验证关键字段是否存在
            var requiredFields = ['sendProvName', 'checkAuditName', 'auditTeam'];
            requiredFields.forEach(function(field) {
                if (!params[field]) {
                    params[field] = $("#" + field).val() || "";
                }
            });

            // 调试输出，帮助检查参数完整性
            console.log('getParams 收集到的所有参数:', params);
            console.log('表单字段数量:', Object.keys(params).length);

            return params;
        }

        //选择再审计小组成员
        window.queryUser = function () {
            // 获取是否对下检查的值
            var checkBelow = $("#isSubCheck").prop("checked") ? "1" : "0";

            var index = layer.open({
                title: '选择再审计人员',
                content: 'views/audit/prj/endaccount/account-book/queryUser.html?accountabilityId=' + accountabilityId + '&checkBelow=' + checkBelow,
                type: 2,
                area: ['50%', '80%'],
                fixed: true,
                maxmin: false,
                resize: false,
                closeBtn: 0,
                btn: ['确认', '取消'],
                yes: function (index, layero) {
                    var userNames = $(layero).find("iframe")[0].contentWindow.getuserNames();

                    // 调用保存再审计小组成员信息接口
                    saveAuditUserList(userNames, function (success, auditType) {
                        if (success) {
                            // 保存成功后更新显示
                            var names = "";
                            for (var a = 0; a < userNames.length; a++) {
                                names += userNames[a].name;
                                if (userNames[a].selectName) {
                                    names += "（" + userNames[a].selectName + "）";
                                }
                                names += " ；";
                            }
                            $("#auditTeam").val(names);

                            // 根据返回的auditType更新组织方式回显
                            if (auditType) {
                                setAuditTypeName(auditType);
                            }

                            layer.close(index);
                        }
                        // 如果保存失败，不关闭窗口，让用户重新操作
                    });
                },
                success: function (layero, index) {
                }
            });
        }

        window.queryCheckLedgersTableInfo = function () {
            // 只有当"是否对下检查"为"是"时才加载抽查项目列表
            var checkBelow = $("#isSubCheck").prop("checked");
            if (!checkBelow) {
                $("#checkProjectSection").hide();
                return;
            }

            $("#checkProjectSection").show();

            // 根据追责状态决定是否显示选择框和操作列
            var isAccountabilityInProgress = (accountabilityStatus == '2');

            // 构建表格列配置
            var tableCols = [];

            // 第一行列配置
            var firstRowCols = [];

            // 如果不是追责中状态，添加选择框
            if (!isAccountabilityInProgress) {
                firstRowCols.push({type: 'checkbox', width: 50, rowspan: 3});
            }

            // 添加序号列
            firstRowCols.push({type: 'numbers', title: '序号', width: 60, align: 'center', rowspan: 3});

            table.render({
                elem: '#checkLedgersTable',
                id: 'checkLedgersTable',
                url: ctx + '/prj/accountabilityfilter/queryFilterProject',
                where: { accountabilityId: accountabilityId },
                page: true,
                even: true, //开启隔行背景
                limits: [10, 30, 60, 120, 300],
                limit: 10,
                //height: 'full-180',
              cols: [[
                    // 动态添加选择框（仅在非追责中状态）
                    ...(isAccountabilityInProgress ? [] : [{type: 'checkbox', width: 50, rowspan: 3}]),
                    {type: 'numbers', title: '序号', width: 60, align: 'center', rowspan: 3},
                    {
                        field: 'orderId',
                        title: '合同/订单编号',
                        width: 200,
                        align: 'center',
                        style: 'text-align: left',
                        rowspan: 3,
                        templet: function(d) {
                            var displayValue = d.orderId || d.contractCode || '';
                            if (displayValue) {
                                // 只有当 existCheckLedgerFlag 等于1时才可点击
                                if (d.existCheckLedgerFlag == '1' || d.existCheckLedgerFlag == 1) {
                                    return '<a href="javascript:void(0)" onclick="showCheckLedgersDetail(\'' +
                                           (d.endAccountAttrId || '') + '\')" style="color: #c20000; text-decoration: underline; cursor: pointer;">' +
                                           displayValue + '</a>';
                                } else {
                                    // 不可点击时显示为普通文本
                                    return '<span style="color: #666;">' + displayValue + '</span>';
                                }
                            } else {
                                return '';
                            }
                        }
                    },
                    {field: 'firstAuditMonth', title: '初审台账账期', width: 120, align: 'center', rowspan: 3},
                    {field: 'auditMonth', title: '台账账期', width: 120, align: 'center', style: 'text-align: center', rowspan: 3},
                    {field: 'ledgersTypeName', title: '台账类型', width: 120, align: 'center', rowspan: 3},
                    {field: 'ledgersFlagName', title: '生成方式', width: 120, align: 'center', rowspan: 3},
                    {field: 'sendProvName', title: '报审省分', width:120, align: 'center',  rowspan: 3},
                    {field: 'sendAreaName', title: '报审地市', width:120, align: 'center', rowspan: 3},
                    {field : 'projectName',title : '项目名称',width :340,align : 'center',style : 'text-align: left', rowspan: 3},
                    {field: 'projectCodeErp', title: '项目ERP编码', width: 150, align: 'center', style: 'text-align: left', rowspan: 3},
                    {field: 'professionOneName', title: 'A级专业名称', width: 120, align: 'center', style: 'text-align: left', rowspan: 3},
                    {field: 'professionTwoName', title: 'B级专业名称', width: 120, align: 'center', style: 'text-align: left', rowspan: 3},
                    {field: 'professionThreeName', title: 'C级专业名称', width: 120, align: 'center', style: 'text-align: left', rowspan: 3},
                    {field: 'designCode', title: '设计批复文号', width: 200, align: 'center', style: 'text-align: left', rowspan: 3},
                    {field: 'designInvest', title: '批复概算投资额', width: 120, align: 'center', style: 'text-align: right', rowspan: 3
                        ,templet: function (d) {
                            return formatNum(d.designInvest);
                        }
                    },
                    {field: 'designBuildInvest', title: '其中：建安投资额', width: 120, align: 'center', style: 'text-align: right', rowspan: 3
                        ,templet: function (d) {
                            return formatNum(d.designBuildInvest);
                        }
                    },
                    {field: 'auditTypeName', title: '项目差异化审计类型', width: 180, align: 'center', style: 'text-align: center', rowspan: 3},
                    {field: 'constructionUnit', title: '施工单位', width: 180, align: 'center', style: 'text-align: left', rowspan: 3},
                    { title: '原结算审计信息', align: 'center', style: 'text-align: center',colspan:48},
                    {field: 'isReAuditCheckName', title: '是否再审计复核', width: 120, align: 'center', style: 'text-align: center', rowspan: 3},
                    {field: 'isReportProvAuditCheckName', title: '复核审计<br>是否上报省分', width: 120, align: 'center', style: 'text-align: center', rowspan: 3},
                    {field: 'isKeyCheckName', title: '是否重点核查', width: 120, align: 'center', style: 'text-align: center', rowspan: 3},
                    { title: '复核结算审计信息', align: 'center', style: 'text-align: center',colspan:35},
                    {field: 'reportFinalUnitName', title: '出具审计报告单位名称', width: 350, align: 'center', style: 'text-align: left', rowspan: 3},
                    {field: 'auditReportCode', title: '审计报告编号', width: 160, align: 'center', style: 'text-align: left', rowspan: 3},
                    {field: 'auditReportName', title: '审计报告名称', width: 160, align: 'center', style: 'text-align: left', rowspan: 3},
                    {field: 'cutRateExecheckCutionCost', title: '施工费再审减率=<br>再审减金额/原始送审金额', width: 170, align: 'center', style: 'text-align: right', rowspan: 3
                        ,templet: function (d) {
                            return formatNum(d.cutRateExecheckCutionCost);
                        }
                    },
                    {field: 'deviationDegree', title: '施工费再审减偏离度=<br>再审减金额/原审减金额', width: 170, align: 'center', style: 'text-align: right', rowspan: 3
                        ,templet: function (d) {
                            if(d.deviationDegree){
                                return d.deviationDegree;
                            }else{
                                return '∞';
                            }
                        }
                    },
                    {field: 'relativeCutRate', title: '相对再审减率=施工费<br>再审减率/本省上年度同类<br>工程施工费平均审减率', width: 170, align: 'center', style: 'text-align: right', rowspan: 3
                        ,templet: function (d) {
                            if(d.relativeCutRate){
                                return d.relativeCutRate;
                            }else{
                                return '∞';
                            }
                        }
                    },
                    { title: '追责信息', align: 'center', style: 'text-align: center',colspan:7},
                    // 动态添加操作列（仅在非追责中状态）
                    ...(isAccountabilityInProgress ? [] : [{title: '操作',width: 100,align: 'center',templet: '#configBar',fixed: 'right',rowspan: 3}])
                ],[
                    {field: 'contractCode', title: '合同/协议编号', width: 140, align: 'center', style: 'text-align: center', rowspan: 2},

                    {field: 'contractOrderName', title: '合同/订单名称', width: 250, align: 'center', style: 'text-align: left', rowspan: 2},
                    {field: 'addedTaxN', title: '合同/订单<br>金额（不含税）', width: 120, align: 'center', style: 'text-align: right', rowspan: 2
                        ,templet: function (d) {
                            return formatNum(d.addedTaxN);
                        }
                    },
                    {field: 'sendOrgName', title: '报审部门', width: 200, align: 'center', style: 'text-align: left', rowspan: 2},
                    {field: 'sendUserName', title: '报审人', width:100, align: 'center', style: 'text-align: center', rowspan: 2},
                    {field: 'endAccountCode', title: '审计工单号/批次号', width: 130, align: 'center', style: 'text-align: center', rowspan: 2},
                    { title: '送审金额（元）', align: 'center', style: 'text-align: center',colspan:6},
                    { title: '审定金额（元）', align: 'center', style: 'text-align: center',colspan:6},
                    { title: '审减金额（元）', align: 'center', style: 'text-align: center',colspan:6},
                    { title: '审减率（%）', align: 'center', style: 'text-align: center',colspan:6},
                    { title: '审计费（元）', align: 'center', style: 'text-align: center',colspan:7},
                    {field: 'auditStartDate', title: '审计开始时间', width: 150, align: 'center', style: 'text-align: center', rowspan: 2},
                    {field: 'auditSubmitDate', title: '审计结果提交时间', width: 150, align: 'center', style: 'text-align: center', rowspan: 2},
                    {field: 'receiveUserName', title: '接审人', width: 100, align: 'center', style: 'text-align: center', rowspan: 2},
                    {field: 'auditUserName', title: '审计人员', width: 100, align: 'center', style: 'text-align: center', rowspan: 2},
                    {field: 'entrustUserName', title: '委托联系人', width:100, align: 'center', style: 'text-align: center', rowspan: 2},
                    {field: 'auditTypeIdName', title: '审计方式', width: 100, align: 'center', style: 'text-align: center', rowspan: 2},
                    {field: 'auditUnit', title: '审计中介机构名称', width: 300, align: 'center', style: 'text-align: left', rowspan: 2},
                    {field: 'auditLiableUserName', title: '审计组负责人', width: 120, align: 'center', style: 'text-align: center', rowspan: 2},
                    {field: 'auditLeaderUser', title: '审计组组长', width: 120, align: 'center', style: 'text-align: center', rowspan: 2},
                    {field: 'auditCrewUser', title: '审计组组员', width: 120, align: 'center', style: 'text-align: center', rowspan: 2},
                    {field: 'checkAuditTypeName', title: '复核审计方式', width: 100, align: 'center', style: 'text-align: center', rowspan: 2},
                    {field: 'checkAuditUnit', title: '复核审计单位', width: 300, align: 'center', style: 'text-align: left', rowspan: 2},
                    {field: 'reAuditStartTime', title: '复核审计开始时间（再审计单<br>位接口人指派并接收之日）', width: 200, align: 'center', style: 'text-align: center', rowspan: 2},
                    {field: 'reAuditEndTime', title: '复核审计完成时间（审计部<br>部门经理确认再审计结果之日）', width: 200, align: 'center', style: 'text-align: center', rowspan: 2},
                    { title: '复核审定金额（元）', align: 'center', style: 'text-align: center',colspan:6},
                    { title: '复核审减金额（元）', align: 'center', style: 'text-align: center',colspan:6},
                    { title: '复核审减率（%）', align: 'center', style: 'text-align: center',colspan:6},
                    { title: '复核审计费（元）', align: 'center', style: 'text-align: center',colspan:7},
                    { title: '复核审计发现的主要问题', align: 'center', style: 'text-align: center',colspan:3},
                    { title: '复核审计单位信息', align: 'center', style: 'text-align: center',colspan:3},
                    {field: 'evaluateStandardName', title: '评价标准（再审减偏离度<br>、相对再审减率孰高）', width: 180, align: 'center', style: 'text-align: center', rowspan: 2},
                    {field: 'accountabilityFlagName', title: '是否追责', width: 100, align: 'center', style: 'text-align: center', rowspan: 2},
                    {field: 'accountabilityLevelName', title: '追责档次', width: 100, align: 'center', style: 'text-align: center', rowspan: 2},
                    {field: 'proportionStartEnd', title: '赔偿复核审减金<br>额的比例区间', width: 120, align: 'center', style: 'text-align: center', rowspan: 2},
                    {field: 'proportion', title: '实际赔偿复核审<br>减金额的比例(%)', width:120, align: 'center', style: 'text-align: right', rowspan: 2
                        ,templet: function (d) {
                            if(d.proportion){
                                return formatNum(d.proportion);
                            }else{
                                return '';
                            }

                        }
                    },
                    {field: 'accountabilityAmount', title: '退还审计<br>费（元）', width:120, align: 'center', style: 'text-align: right', rowspan: 2
                        ,templet: function (d) {
                            if(d.accountabilityAmount){
                                return formatNum(d.accountabilityAmount);
                            }else{
                                return ''
                            }

                        }
                    },
                    {field: 'realityCheckCutAmount', title: '赔偿损失<br>金额（元）', width: 120, align: 'center', style: 'text-align: right', rowspan: 2
                        ,templet: function (d) {
                            if(d.realityCheckCutAmount) {
                                return formatNum(d.realityCheckCutAmount);
                            }else{
                                return '';
                            }
                        }
                    },
                ],[
                    //初审送审
                    {field: 'sendAmountExecutionCost', title: '施工费', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.sendAmountExecutionCost);
                        }
                    },
                    {field: 'sendAmountMaterialsCostB', title: '其中:乙供材料费', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.sendAmountMaterialsCostB);
                        }
                    },
                    {field: 'sendAmountMaterialsCostA', title: '甲供材料费', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.sendAmountMaterialsCostA);
                        }
                    },
                    {field: 'sendAmountEquit', title: '甲供设备费', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.sendAmountEquit);
                        }
                    },
                    {field: 'sendAmountOther', title: '其它', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.sendAmountOther);
                        }
                    },
                    {field: 'sendAmount', title: '合计', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.sendAmount);
                        }
                    },
                    //初审审定
                    {field: 'fixAmountExecutionCost', title: '施工费', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.fixAmountExecutionCost);
                        }
                    },
                    {field: 'fixAmountMaterialsCostB', title: '其中:乙供材料费', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.fixAmountMaterialsCostB);
                        }
                    },
                    {field: 'fixAmountMaterialsCostA', title: '甲供材料费', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.fixAmountMaterialsCostA);
                        }
                    },
                    {field: 'fixAmountEquit', title: '甲供设备费', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.fixAmountEquit);
                        }
                    },
                    {field: 'fixAmountOther', title: '其它', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.fixAmountOther);
                        }
                    },
                    {field: 'fixAmount', title: '合计', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.fixAmount);
                        }
                    },
                    //初审审减
                    {field: 'cutAmountExecutionCost', title: '施工费', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.cutAmountExecutionCost);
                        }
                    },
                    {field: 'cutAmountMaterialsCostB', title: '其中:乙供材料费', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.cutAmountMaterialsCostB);
                        }
                    },
                    {field: 'cutAmountMaterialsCostA', title: '甲供材料费', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.cutAmountMaterialsCostA);
                        }
                    },
                    {field: 'cutAmountEquit', title: '甲供设备费', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.cutAmountEquit);
                        }
                    },
                    {field: 'cutAmountOther', title: '其它', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.cutAmountOther);
                        }
                    },
                    {field: 'cutAmount', title: '合计', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.cutAmount);
                        }
                    },
                    //初审审减率
                    {field: 'cutRateExecutionCost', title: '施工费', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.cutRateExecutionCost);
                        }
                    },
                    {field: 'cutRateMaterialsCostB', title: '其中:乙供材料费', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.cutRateMaterialsCostB);
                        }
                    },
                    {field: 'cutRateMaterialsCostA', title: '甲供材料费', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.cutRateMaterialsCostA);
                        }
                    },
                    {field: 'cutRateEquit', title: '甲供设备费', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.cutRateEquit);
                        }
                    },
                    {field: 'cutRateOther', title: '其它', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.cutRateOther);
                        }
                    },
                    {field: 'cutRate', title: '合计', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.cutRate);
                        }
                    },
                    //初审审计费
                    {field: 'baseCost', title: '基本收费', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.baseCost);
                        }
                    },
                    {field: 'benefitCost', title: '效益收费', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.benefitCost);
                        }
                    },
                    {field: 'costCheckChange', title: '审计调整金额', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.costCheckChange);
                        }
                    },
                    {field: 'costWithTax', title: '含税', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.costWithTax);
                        }
                    },
                    {field: 'costOutTax', title: '不含税计', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.costOutTax);
                        }
                    },
                    {field: 'costTaxAmount', title: '税金', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.costTaxAmount);
                        }
                    },
                    {field: 'costRate', title: '税率（%）', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.costRate);
                        }
                    },
                    //复核审定
                    {field: 'checkFixAmountExecutionCost', title: '施工费', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkFixAmountExecutionCost);
                        }
                    },
                    {field: 'checkFixAmountMaterialsCostB', title: '其中:乙供材料费', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkFixAmountMaterialsCostB);
                        }
                    },
                    {field: 'checkFixAmountMaterialsCostA', title: '甲供材料费', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkFixAmountMaterialsCostA);
                        }
                    },
                    {field: 'checkFixAmountEquit', title: '甲供设备费', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkFixAmountEquit);
                        }
                    },
                    {field: 'checkFixAmountOther', title: '其它', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkFixAmountOther);
                        }
                    },
                    {field: 'checkFixAmount', title: '合计', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkFixAmount);
                        }
                    },
                    //复核审减
                    {field: 'checkCutAmountExecutionCost', title: '施工费', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkCutAmountExecutionCost);
                        }
                    },
                    {field: 'checkCutAmountMaterialsCostB', title: '其中:乙供材料费', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkCutAmountMaterialsCostB);
                        }
                    },
                    {field: 'checkCutAmountMaterialsCostA', title: '甲供材料费', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkCutAmountMaterialsCostA);
                        }
                    },
                    {field: 'checkCutAmountEquit', title: '甲供设备费', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkCutAmountEquit);
                        }
                    },
                    {field: 'checkCutAmountOther', title: '其它', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkCutAmountOther);
                        }
                    },
                    {field: 'checkCutAmount', title: '合计', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkCutAmount);
                        }
                    },
                    //复核审减率
                    {field: 'checkCutRateExecutionCost', title: '施工费', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkCutRateExecutionCost);
                        }
                    },
                    {field: 'checkCutRateMaterialsCostB', title: '其中:乙供材料费', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkCutRateMaterialsCostB);
                        }
                    },
                    {field: 'checkCutRateMaterialsCostA', title: '甲供材料费', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkCutRateMaterialsCostA);
                        }
                    },
                    {field: 'checkCutRateEquit', title: '甲供设备费', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkCutRateEquit);
                        }
                    },
                    {field: 'checkCutRateOther', title: '其它', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkCutRateOther);
                        }
                    },
                    {field: 'checkCutRate', title: '合计', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkCutRate);
                        }
                    },
                    //复核审计费（元）
                    {field: 'checkBaseCost', title: '基本收费', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkBaseCost);
                        }
                    },
                    {field: 'checkBenefitCost', title: '效益收费', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkBenefitCost);
                        }
                    },
                    {field: 'checkCostCheckChange', title: '审计调整金额', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkCostCheckChange);
                        }
                    },
                    {field: 'checkCostWithTax', title: '含税', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkCostWithTax);
                        }
                    },
                    {field: 'checkCostOutTax', title: '不含税', width:120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkCostOutTax);
                        }
                    },
                    {field: 'checkCostTaxAmount', title: '税金', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkCostTaxAmount);
                        }
                    },
                    {field: 'checkCostRate', title: '税率（%）', width: 120, align: 'center', style: 'text-align: right'
                        ,templet: function (d) {
                            return formatNum(d.checkCostRate);
                        }
                    },
                    //复核审计发现的主要问题
                    {field: 'isAuditNormativeProblemName', title: '审计过程的规范性<br>是否存在问题', width:120, align: 'center', style: 'text-align: center'},
                    {field: 'isAuditFullProblemName', title: '审计依据的充分性<br>是否存在问题', width: 120, align: 'center', style: 'text-align: center'},
                    {field: 'isAuditAccuracyProblemName', title: '审计结果的准确性<br>是否存在问题', width: 120, align: 'center', style: 'text-align: center'},
                    //复核审计单位信息
                    {field: 'checkAuditLiableUserName', title: '审计组负责人', width: 100, align: 'center', style: 'text-align: center'},
                    {field: 'checkAuditLeaderUser', title: '审计组组长', width: 100, align: 'center', style: 'text-align: center'},
                    {field: 'checkAuditCrewUser', title: '审计组组员', width: 100, align: 'center', style: 'text-align: center'},
                ]
                ],
                done: function () {
                    // 表格渲染完成后，初始化批量删除按钮状态
                    updateBatchDeleteButtonStatus();
                }
            });
        }

        // 保存再审计小组成员信息
        window.saveAuditUserList = function (userList, callback) {
            var indexZG = layer.load(1, {
                shade: [0.4, '#fff']
            });

            $.ajax({
                url: ctx + "/prj/overallAccountabilitynew/saveAuditUserList",
                type: "POST",
                dataType: "json",
                data: JSON.stringify({
                    accountabilityId: accountabilityId,
                    userList: userList
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    layer.close(indexZG);
                    if (ret.httpCode == 200) {
                        frm.alertMsg("保存再审计小组成员成功");
                        // 调用回调函数，传递成功状态和auditType
                        if (callback) {
                            callback(true, ret.data);
                        }
                    } else {
                        frm.error(ret.msg || "保存再审计小组成员失败");
                        // 调用回调函数，传递失败状态
                        if (callback) {
                            callback(false);
                        }
                    }
                },
                error: function (e) {
                    layer.close(indexZG);
                    frm.error("网络异常，保存再审计小组成员失败");
                    // 调用回调函数，传递失败状态
                    if (callback) {
                        callback(false);
                    }
                }
            });
        };

        /*****************************生成整体追责***********************************/
        /**
         * 生成整体追责按钮
         */
        window.createOverallAccountabilityInfo = function () {
            // 验证追责主键
            if (!accountabilityId) {
                layer.msg('缺少追责主表主键参数！', { icon: 2 });
                return;
            }

            // 获取基本信息用于确认对话框显示
            var checkMonth = $("#checkMonth").val();
            var auditPeriod = $("#auditPeriod").val();
            var checkAuditName = $("#checkAuditName").val();
            var auditTeam = $("#auditTeam").val();
            var isSubCheck = $("#isSubCheck").prop("checked");

            // 验证再审计期间是否选择
            if (!checkMonth || checkMonth.trim() === '') {
                layer.msg('请先选择再审计期间！', { icon: 2 });

                // 添加视觉提示效果
                var $checkMonth = $("#checkMonth");
                $checkMonth.addClass('field-required');
                $checkMonth.focus();

                // 3秒后移除提示样式
                setTimeout(function() {
                    $checkMonth.removeClass('field-required');
                }, 3000);

                return;
            }

            // 确认对话框
            var confirmMsg = '确定要生成整体追责吗？<br><span style="color: #ff5722;">注意：此操作将重新生成追责数据！</span><br>' +
                '<div style="margin-top: 10px; text-align: left; color: #666; font-size: 12px;">' +
                '<div>再审计名称：' + checkAuditName + '</div>' +
                '<div>再审计期间：' + checkMonth + '</div>' +
                '<div>审计时间区间：' + auditPeriod + '</div>' +
                '<div>审计小组：' +  auditTeam + '</div>' +
                '<div>是否对下检查：' + (isSubCheck ? '<span style="color: #5FB878;">是</span>' : '<span style="color: #ff5722;">否</span>') + '</div>' +
                '</div>';

                     // 获取再审计期间参数
            var checkMonth = $("#checkMonth").val();
            var params = {
                accountabilityId: accountabilityId,
                checkBelow: $("#isSubCheck").prop("checked") ? "1" : "0"
            };

            if (checkMonth) {
                params.selectStartMonth = checkMonth.substr(0, 6);
                params.selectEndMonth = checkMonth.substr(9);
            }
            layer.confirm(confirmMsg, {
                icon: 3,
                title: '确认生成整体追责',
                area: ['500px', '280px']
            }, function (index) {
                // 显示加载遮盖层
                var indexZG = layer.load(1, {
                    shade: [0.4, '#fff'] //0.4透明度的白色背景
                });

                $.ajax({
                    url: ctx + "/prj/overallAccountability/reCreateAccountOverallAccountabilityInfo",
                    type: 'POST',
                    dataType: "JSON",
                    data: JSON.stringify(params),
                    contentType: "application/json;charset=UTF-8",
                    success: function (res) {
                        layer.close(indexZG);

                        if (res.httpCode == 200) {
                            if (res.data) {
                                var successMsg = '生成整体追责成功！';
                                if (res.data.length !== undefined) {
                                    successMsg += '共生成 ' + res.data.length + ' 条追责记录。';
                                }
                                layer.msg(successMsg, { icon: 1, time: 3000 });

                                //加载数据表格
                                queryAccountabilityTable(res.data);
                            } else {
                                layer.msg("该再审计期间暂无数据", { icon: 0 });
                            }
                        } else {
                            layer.msg(res.msg || '生成整体追责失败！', { icon: 2 });

                        }
                    },
                    error: function (xhr, status, error) {
                        layer.close(indexZG);

                        var errorMsg = '网络异常，生成整体追责失败！';
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response && response.msg) {
                                errorMsg = response.msg;
                            }
                        } catch (e) {
                            // 解析失败，使用默认错误信息
                        }

                        layer.msg(errorMsg, { icon: 2 });
                    }
                });

                // 关闭确认框
                layer.close(index);
            });
        };



        // 整体追责查看台账明细
        window.showCheckLedgersList = function (detailId) {
            var index = window.parent.layer.open({
                type: 2,
                maxmin: true,
                content: 'views/audit/prj/endaccount/endreauditstatic/checkLedgersList.html?accountabilityDetailId=' + detailId
                    + '&type=accountability',
                title: [
                    '再审计台账', 'font-size:14px;background-color: #f2f2f2;'
                ],
                area: ['95%', '98%'],
                fixed: true,
                success: function (layero, index) {
                },
                end: function () {
                }
            });
        };

        // 抽查项目查看台账明细
        window.showCheckLedgersDetail = function (endAccountAttrId) {
            var index = window.parent.layer.open({
                type: 2,
                maxmin: true,
                content: 'views/audit/prj/endaccount/endreauditstatic/checkLedgersList.html?accountabilityId=' + accountabilityId
                    + '&endAccountAttrId=' + endAccountAttrId + '&type=filter',
                title: [
                    '再审计台账', 'font-size:14px;background-color: #f2f2f2;'
                ],
                area: ['95%', '98%'],
                fixed: true,
                success: function (layero, index) {
                },
                end: function () {
                }
            });
        };

        /*****************************生成整体追责***********************************/

        /*****************************追责项目保存和提交***********************************/

        /**
         * 验证必填字段
         */
        function validateRequiredFields() {
            var errors = [];

            // 验证立项单位
            var sendProvName = $("#sendProvName").val();
            if (!sendProvName || sendProvName.trim() === '') {
                errors.push('立项单位不能为空');
            }

            // 验证再审计期间
            var checkMonth = $("#checkMonth").val();
            if (!checkMonth || checkMonth.trim() === '') {
                errors.push('再审计期间不能为空');
            }

            // 验证再审计名称
            var checkAuditName = $("#checkAuditName").val();
            if (!checkAuditName || checkAuditName.trim() === '') {
                errors.push('再审计名称不能为空');
            }

            // 验证审计时间区间
            var auditPeriod = $("#auditPeriod").val();
            if (!auditPeriod || auditPeriod.trim() === '') {
                errors.push('审计时间区间不能为空');
            }

            // 验证是否对下检查（这个字段是复选框，默认有值，但我们可以检查是否已设置）
            // 复选框默认是有值的，所以这里不需要特别验证

            // 验证再审计小组成员
            var auditTeam = $("#auditTeam").val();
            if (!auditTeam || auditTeam.trim() === '') {
                errors.push('再审计小组成员不能为空');
            }

            return errors;
        }

        /**
         * 添加必填字段实时验证
         */
        function addRequiredFieldValidation() {
            // 再审计名称实时验证
            $("#checkAuditName").on('blur', function () {
                var value = $(this).val();
                if (!value || value.trim() === '') {
                    $(this).css('border-color', '#ff5722');
                    layer.tips('再审计名称不能为空', this, {
                        tips: [1, '#ff5722'],
                        time: 2000
                    });
                } else {
                    $(this).css('border-color', '#e6e6e6');
                }
            });

            // 再审计小组成员实时验证
            $("#auditTeam").on('blur', function () {
                var value = $(this).val();
                if (!value || value.trim() === '') {
                    $(this).css('border-color', '#ff5722');
                    layer.tips('再审计小组成员不能为空', this, {
                        tips: [1, '#ff5722'],
                        time: 2000
                    });
                } else {
                    $(this).css('border-color', '#e6e6e6');
                }
            });

            // 输入时恢复正常边框颜色
            $("#checkAuditName, #auditTeam").on('input', function () {
                $(this).css('border-color', '#e6e6e6');
            });
        }
        /**
         * 保存追责项目信息
         */
        window.saveAccountabilityBtn = function () {
            // 验证必填字段
            var errors = validateRequiredFields();
            if (errors.length > 0) {
                layer.alert('请完善以下必填信息：<br>' + errors.join('<br>'), {
                    icon: 2,
                    title: '验证失败',
                    area: ['400px', 'auto']
                });
                return;
            }

            var indexZG = layer.load(1, {
                shade: [0.4, '#fff']
            });
            var params = getParams();
            params.overallAccountabilityList = table.cache['accountabilityTable'];
            // 添加追责主表主键
            params.accountabilityId = accountabilityId;

            // 验证追责主表主键
            if (!params.accountabilityId) {
                layer.msg('缺少追责主表主键参数！', {icon: 2});
                return;
            }

            console.log('保存参数：', {
                accountabilityId: params.accountabilityId,
                overallAccountabilityListCount: params.overallAccountabilityList ? params.overallAccountabilityList.length : 0
            });

            $.ajax({
                url: ctx + "/prj/overallAccountability/saveOverallAccountabilityInfo",
                type: 'POST',
                dataType: "JSON",
                data: JSON.stringify({
                    // 追责主表主键
                    accountabilityId: params.accountabilityId,

                    // 基本信息表单字段
                    sendProvName: params.sendProvName,
                    sendProvCode: params.sendProvCode,
                    checkAuditName: params.checkAuditName,
                    checkMonth: params.checkMonth,
                    checkStartMonth: params.checkStartMonth,
                    checkEndMonth: params.checkEndMonth,
                    selectStartMonth: params.selectStartMonth,
                    selectEndMonth: params.selectEndMonth,
                    auditPeriod: params.auditPeriod,
                    auditStartMonth: params.auditStartMonth,
                    auditEndMonth: params.auditEndMonth,
                    auditDateStart: params.auditDateStart,
                    auditDateEnd: params.auditDateEnd,
                    checkBelow: params.checkBelow,
                    auditTeam: params.auditTeam,
                    auditType: params.auditType,
                    auditTypeName: params.auditTypeName,
                    accountabilityStatus: params.accountabilityStatus,
                    reformStatus: params.reformStatus,

                    // 项目台账列表
                    overallAccountabilityList: params.overallAccountabilityList
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        layer.msg('保存成功！');
                           // closeWindowAndRefreshParent();

                        layer.close(indexZG);
                    } else {
                        layer.close(indexZG);
                        frm.error(res.msg);
                    }
                }
            });
        };

        /**
         * 提交追责项目信息
         */
        window.submitAccountabilityBtn = function () {
            // 验证必填字段
            var errors = validateRequiredFields();
            if (errors.length > 0) {
                layer.alert('请完善以下必填信息：<br>' + errors.join('<br>'), {
                    icon: 2,
                    title: '验证失败',
                    area: ['400px', 'auto']
                });
                return;
            }

            frm.confirm("提交后无法进行修改，请确认", function () {
                var indexZG = layer.load(1, {
                    shade: [0.4, '#fff']
                });
                var params = getParams();
                params.overallAccountabilityList = table.cache['accountabilityTable'];
                // 添加再审计名称
                params.checkAuditName = $("#checkAuditName").val();
                // 添加整体追责主表主键
                params.accountabilityId = accountabilityId;

                // 验证再审计名称
                if (!params.checkAuditName || params.checkAuditName.trim() === '') {
                    layer.close(indexZG);
                    layer.msg('再审计名称不能为空！', {icon: 2});
                    return;
                }

                // 验证整体追责主表主键
                if (!params.accountabilityId) {
                    layer.close(indexZG);
                    layer.msg('缺少整体追责主表主键参数！', {icon: 2});
                    return;
                }

                console.log('提交参数：', {
                    checkAuditName: params.checkAuditName,
                    accountabilityId: params.accountabilityId,
                    overallAccountabilityListCount: params.overallAccountabilityList ? params.overallAccountabilityList.length : 0
                });

                $.ajax({
                    url: ctx + "/prj/overallAccountability/submitAccountabilityInfo",
                    type: 'POST',
                    dataType: "JSON",
                    data: JSON.stringify({
                        // 追责主表主键
                        accountabilityId: params.accountabilityId,

                        // 基本信息表单字段
                        sendProvName: params.sendProvName,
                        sendProvCode: params.sendProvCode,
                        checkAuditName: params.checkAuditName,
                        checkMonth: params.checkMonth,
                        checkStartMonth: params.checkStartMonth,
                        checkEndMonth: params.checkEndMonth,
                        selectStartMonth: params.selectStartMonth,
                        selectEndMonth: params.selectEndMonth,
                        auditPeriod: params.auditPeriod,
                        auditStartMonth: params.auditStartMonth,
                        auditEndMonth: params.auditEndMonth,
                        auditDateStart: params.auditDateStart,
                        auditDateEnd: params.auditDateEnd,
                        checkBelow: params.checkBelow,
                        auditTeam: params.auditTeam,
                        auditType: params.auditType,
                        auditTypeName: params.auditTypeName,
                        accountabilityStatus: params.accountabilityStatus,
                        reformStatus: params.reformStatus,

                        // 项目台账列表
                        overallAccountabilityList: params.overallAccountabilityList
                    }),
                    contentType: "application/json;charset=UTF-8",
                    success: function (res) {
                        layer.close(indexZG);
                        if (res.httpCode == 200) {
                            layer.close(indexZG);
                            if(res.httpCode == 200){
                                var msg1 = res.msg1;
                                var msg2 = res.msg2;
                                if(!msg1 && !msg2){
                                    layer.msg('提交成功！', {icon: 1,time: '1200'}, function () {
                                        closeCurIndex();
                                    });
                                }
                                else if(msg2){
                                    frm.alertMsg(msg2,'2');
                                }else if(msg1){
                                    frm.confirm(msg1,function(){
                                        submitUnValidate(params);
                                    })

                                }
                            }else{
                                frm.error(res.msg);
                            }
                        } else {
                            frm.error(res.msg);
                        }
                    }
                });
            });
        };

        window.closeCurIndex = function(){
            var curIndex = parent.layer.getFrameIndex(window.name);
            parent.layer.close(curIndex);
        }

        /**
         * 无校验的提交（前面的校验已通过）
         */
        window.submitUnValidate = function (params) {
            var indexZG = layer.load(1, {
                shade: [0.4, '#fff']
            });

            $.ajax({
                url: ctx + "/prj/overallAccountability/submitUnValidate",
                type: 'POST',
                dataType: "JSON",
                data: JSON.stringify({
                    // 追责主表主键
                    accountabilityId: params.accountabilityId,

                    // 基本信息表单字段
                    sendProvName: params.sendProvName,
                    sendProvCode: params.sendProvCode,
                    checkAuditName: params.checkAuditName,
                    checkMonth: params.checkMonth,
                    checkStartMonth: params.checkStartMonth,
                    checkEndMonth: params.checkEndMonth,
                    selectStartMonth: params.selectStartMonth,
                    selectEndMonth: params.selectEndMonth,
                    auditPeriod: params.auditPeriod,
                    auditStartMonth: params.auditStartMonth,
                    auditEndMonth: params.auditEndMonth,
                    auditDateStart: params.auditDateStart,
                    auditDateEnd: params.auditDateEnd,
                    checkBelow: params.checkBelow,
                    auditTeam: params.auditTeam,
                    auditType: params.auditType,
                    auditTypeName: params.auditTypeName,
                    accountabilityStatus: params.accountabilityStatus,
                    reformStatus: params.reformStatus,

                    // 项目台账列表
                    overallAccountabilityList: params.overallAccountabilityList
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    layer.close(indexZG);
                    if (res.httpCode == 200) {
                        layer.msg('提交成功！', { icon: 1, time: '2000' }, function () {
                            // 提交成功后关闭弹窗并刷新父页面表格
                            closeWindowAndRefreshParent();
                        });
                    } else {
                        frm.error(res.msg);
                    }
                },
                error: function() {
                    layer.close(indexZG);
                    frm.error("网络异常，提交失败");
                }
            });
        };

        /**
         * 关闭弹窗
         */
        function closeWindowAndRefreshParent() {
            try {
                // 获取父页面的window对象
                var parentWindow = window.parent;

                // 如果存在父页面，尝试关闭弹窗
                if (parentWindow && parentWindow !== window) {
                    // 尝试通过layer关闭弹窗
                    if (typeof parentWindow.layer !== 'undefined') {
                        parentWindow.layer.closeAll();
                    } else {
                        // 如果layer不可用，尝试关闭当前窗口
                        window.close();
                    }
                } else {
                    // 如果没有父页面，直接关闭当前窗口
                    if (window.opener) {
                        window.close();
                    } else {
                        // 如果无法关闭窗口，可以考虑跳转到其他页面
                        console.log('无法关闭窗口');
                    }
                }
            } catch (e) {
                console.error('关闭弹窗时出错:', e);
                // 出错时至少尝试关闭弹窗
                try {
                    if (window.parent && window.parent.layer) {
                        window.parent.layer.closeAll();
                    }
                } catch (e2) {
                    console.error('关闭弹窗失败:', e2);
                }
            }
        }

        /*****************************追责项目保存和提交***********************************/

        /*****************************项目筛选***********************************/

        /**
         * 验证基本信息是否填写完整
         */
        function validateBasicInfo() {
            var result = {
                isValid: true,
                message: '',
                focusElement: null
            };

            // 先清除所有字段的错误样式
            $('.field-required').removeClass('field-required');

            // 检查再审计名称
            var checkAuditName = $("#checkAuditName").val();
            if (!checkAuditName || checkAuditName.trim() === '') {
                result.isValid = false;
                result.message = '请填写再审计名称！';
                result.focusElement = '#checkAuditName';
                addFieldErrorStyle('#checkAuditName');
                return result;
            }

            // 检查再审计期间
            var checkMonth = $("#checkMonth").val();
            if (!checkMonth || checkMonth.trim() === '') {
                result.isValid = false;
                result.message = '请选择再审计期间！';
                result.focusElement = '#checkMonth';
                addFieldErrorStyle('#checkMonth');
                return result;
            }

            // 检查审计时间区间
            var auditPeriod = $("#auditPeriod").val();
            if (!auditPeriod || auditPeriod.trim() === '') {
                result.isValid = false;
                result.message = '请选择审计时间区间！';
                result.focusElement = '#auditPeriod';
                addFieldErrorStyle('#auditPeriod');
                return result;
            }

            // 检查再审计小组成员
            var auditTeam = $("#auditTeam").val();
            if (!auditTeam || auditTeam.trim() === '') {
                result.isValid = false;
                result.message = '请选择再审计小组成员！';
                result.focusElement = '#auditTeam';
                addFieldErrorStyle('#auditTeam');
                return result;
            }

            return result;
        }

        /**
         * 为字段添加错误提示样式
         */
        function addFieldErrorStyle(selector) {
            var $field = $(selector);
            $field.addClass('field-required');
            $field.focus();

            // 3秒后移除提示样式
            setTimeout(function() {
                $field.removeClass('field-required');
            }, 3000);
        }
        /**
         * 项目筛选
         */
        window.projectFilter = function () {
            // 验证accountabilityId是否存在
            if (!accountabilityId) {
                layer.msg('缺少追责主表主键参数！', {icon: 2});
                return;
            }

            // 逐个检查基本信息是否填写
           // var validationResult = validateBasicInfo();
           // if (!validationResult.isValid) {
               // layer.msg(validationResult.message, {icon: 2});
                // 聚焦到第一个未填写的字段
               // if (validationResult.focusElement) {
                  //  $(validationResult.focusElement).focus();
              //  }
               // return;
          //  }

            // 获取审计时间区间参数
            var checkMonth = $("#checkMonth").val();
            var url = 'views/audit/prj/endaccount/account-book/choose.html?accountabilityId=' + accountabilityId;
            if (checkMonth) {
                url += '&checkMonth=' + encodeURIComponent(checkMonth);
            }

            var index = top.layer.open({
                title: '项目筛选',
                content: url,
                type: 2,
                area: ['95%', '85%'],
                fixed: true,
                maxmin: true,
                resize: true,
                closeBtn: 1,
                shadeClose: false,
                btn: ['关闭'],
                btn0: function (index, layero) {
                    // 关闭弹窗并刷新表格
                    layer.close(index);
                    // 刷新抽查项目表格
                    queryCheckLedgersTableInfo();
                    return false; // 阻止默认行为
                },
                success: function (layero, index) {
                    // 页面加载完成后的回调

                        // 关闭弹窗并刷新表格
                    layer.close(index);
                    // 刷新抽查项目表格
                    queryCheckLedgersTableInfo();
                },
                end: function() {
                    // 弹窗关闭后的回调（无论如何关闭都会执行）
                    // 作为备用方案，确保表格刷新
                    queryCheckLedgersTableInfo();
                }
            });
        };



        /*****************************项目筛选***********************************/

        /*****************************合同/订单详情查看***********************************/
        /**
         * 查看合同/订单详情
         */
        window.viewContractDetail = function(orderId, contractCode, contractOrderName) {
            var displayTitle = '';
            var displayContent = '';

            // 确定显示的标题和内容
            if (orderId) {
                displayTitle = '订单详情';
                displayContent = '<div style="padding: 20px; line-height: 1.8;">' +
                    '<div><strong>订单编号：</strong>' + orderId + '</div>' +
                    (contractOrderName ? '<div><strong>订单名称：</strong>' + contractOrderName + '</div>' : '') +
                    (contractCode ? '<div><strong>关联合同编号：</strong>' + contractCode + '</div>' : '') +
                    '</div>';
            } else if (contractCode) {
                displayTitle = '合同详情';
                displayContent = '<div style="padding: 20px; line-height: 1.8;">' +
                    '<div><strong>合同编号：</strong>' + contractCode + '</div>' +
                    (contractOrderName ? '<div><strong>合同名称：</strong>' + contractOrderName + '</div>' : '') +
                    '</div>';
            } else {
                layer.msg('暂无合同/订单信息！', {icon: 2});
                return;
            }

            // 显示详情弹窗
            layer.open({
                type: 1,
                title: [
                    '<i class="iconfont">&#xe6b6;</i> ' + displayTitle,
                    'font-size:16px;color:#333;font-weight:bold;'
                ],
                content: displayContent,
                area: ['500px', '300px'],
                btn: ['关闭'],
                yes: function(index) {
                    layer.close(index);
                }
            });
        };

        /*****************************项目删除***********************************/
        /**
         * 单个删除方法
         */
        window.deleteSingleItem = function(endAccountAttrId, projectName) {
            if (!endAccountAttrId) {
                layer.msg('缺少删除参数！', {icon: 2});
                return;
            }

            // 验证 accountabilityId
            if (!accountabilityId) {
                layer.msg('缺少追责主表主键参数！', {icon: 2});
                return;
            }

            layer.confirm('确定要删除项目"' + (projectName || '该项目') + '"吗？', {
                icon: 3,
                title: '确认删除'
            }, function(index) {
                // 显示加载提示
                var loadingIndex = layer.msg('正在删除，请稍候...', {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });

                $.ajax({
                    url: ctx + '/prj/accountabilityfilter/delProjectFilterInfo',
                    type: 'POST',
                    dataType: 'JSON',
                    contentType: 'application/json;charset=UTF-8',
                    data: JSON.stringify({
                        endAccountAttrId: endAccountAttrId,
                        accountabilityId: accountabilityId
                    }),
                    success: function(res) {
                        layer.close(loadingIndex);
                        if (res.httpCode == 200) {
                            layer.msg('删除成功！', {icon: 1});
                            // 刷新抽查项目表格
                            queryCheckLedgersTableInfo();
                            //刷新项目台账列表
                            queryAccountabilityTable();
                            // 关闭确认框
                            layer.close(index);
                        } else {
                            layer.msg(res.msg || '删除失败！', {icon: 2});
                        }
                    },
                    error: function() {
                        layer.close(loadingIndex);
                        layer.msg('网络异常，删除失败！', {icon: 2});
                    }
                });
            });
        };

        /**
         * 批量删除方法
         */
        window.batchDeleteItems = function() {
            // 验证 accountabilityId
            if (!accountabilityId) {
                layer.msg('缺少追责主表主键参数！', {icon: 2});
                return;
            }

            // 获取选中的行数据
            var checkStatus = table.checkStatus('checkLedgersTable');
            var data = checkStatus.data;

            if (data.length === 0) {
                layer.msg('请至少选择一条记录！', {icon: 2});
                return;
            }

            // 获取选中的ID列表
            var endAccountAttrIds = [];
            $.each(data, function(index, item) {
                if (item.endAccountAttrId) {
                    endAccountAttrIds.push(item.endAccountAttrId);
                } else if (item.id) {
                    endAccountAttrIds.push(item.id);
                } else if (item.ID) {
                    endAccountAttrIds.push(item.ID);
                }
            });

            if (endAccountAttrIds.length === 0) {
                layer.msg('获取选中记录ID失败！', {icon: 2});
                return;
            }

            layer.confirm('确定要删除选中的 ' + data.length + ' 条记录吗？', {
                icon: 3,
                title: '确认批量删除'
            }, function(index) {
                // 显示加载提示
                var loadingIndex = layer.msg('正在批量删除，请稍候...', {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });

                $.ajax({
                    url: ctx + '/prj/accountabilityfilter/batchDelProjectFilterInfo',
                    type: 'POST',
                    dataType: 'JSON',
                    contentType: 'application/json;charset=UTF-8',
                    data: JSON.stringify({
                        endAccountAttrIds: endAccountAttrIds,
                        accountabilityId: accountabilityId
                    }),
                    success: function(res) {
                        layer.close(loadingIndex);
                        if (res.httpCode == 200) {
                            layer.msg('批量删除成功！', {icon: 1});
                            // 刷新表格
                            queryCheckLedgersTableInfo();
                            // 关闭确认框
                            layer.close(index);
                        } else {
                            layer.msg(res.msg || '批量删除失败！', {icon: 2});
                        }
                    },
                    error: function() {
                        layer.close(loadingIndex);
                        layer.msg('网络异常，批量删除失败！', {icon: 2});
                    }
                });
            });
        };

        /**
         * 更新批量删除按钮状态
         */
        function updateBatchDeleteButtonStatus() {
            // 监听复选框变化
            table.on('checkbox(checkLedgersTable)', function(obj) {
                var checkStatus = table.checkStatus('checkLedgersTable');
                var data = checkStatus.data;

                if (data.length > 0) {
                    $('#batchDeleteBtn').removeClass('layui-btn-disabled').removeAttr('disabled');
                } else {
                    $('#batchDeleteBtn').addClass('layui-btn-disabled').attr('disabled', 'disabled');
                }
            });

            // 初始状态设置按钮为禁用
            $('#batchDeleteBtn').addClass('layui-btn-disabled').attr('disabled', 'disabled');
        }

        /*****************************导出抽查项目***********************************/
        /**
         * 导出抽查项目
         */
        window.exportProject = function () {
            // 验证追责主键
            if (!accountabilityId) {
                layer.msg('缺少追责主表主键参数！', { icon: 2 });
                return;
            }

            // 显示确认对话框
            layer.confirm('确定要导出当前抽查项目数据吗？', {
                icon: 3,
                title: '确认导出'
            }, function(index) {
                // 显示加载提示
                var loadingIndex = layer.msg('正在导出，请稍候...', {
                    icon: 16,
                    shade: 0.3,
                    time: 2000 // 2秒后自动关闭
                });

                // 构建导出参数
                var exportParams = [];
                exportParams.push('accountabilityId=' + encodeURIComponent(accountabilityId));

                // 如果有其他需要的参数，可以在这里添加
                // exportParams.push('otherParam=' + encodeURIComponent(otherValue));

                // 构建导出URL并直接跳转下载
                var url = ctx + '/prj/accountabilityfilter/exportFilterProjectInfo?' + exportParams.join('&');
                window.location.href = url;

                // 关闭确认框
                layer.close(index);

                // 延迟关闭加载提示，给用户足够的反馈时间
                setTimeout(function() {
                    layer.close(loadingIndex);
                }, 2000);
            });
        };


        /*****************************导出抽查项目***********************************/

        /*****************************导入抽查项目***********************************/
        /**
         * 导入抽查项目
         */
        window.importProject = function () {
            // 验证追责主键
            if (!accountabilityId) {
                layer.msg('缺少追责主表主键参数！', { icon: 2 });
                return;
            }

            frm.confirm("请确保抽查项目信息的准确性，导入后将覆盖现有数据，是否确认导入？", function () {
                // 每次都重新创建上传按钮和组件，避免重复使用问题
                createAndTriggerImportUpload();
            });
        };

        /**
         * 创建并触发导入上传
         */
        function createAndTriggerImportUpload() {
            // 生成唯一的按钮ID，避免重复使用问题
            var uniqueId = 'uploadProjectBtn_' + new Date().getTime();

            // 创建新的隐藏按钮
            var uploadBtn = $('<button style="display: none;" id="' + uniqueId + '"></button>');
            $('body').append(uploadBtn);

            // 创建上传组件
            upload.render({
                elem: '#' + uniqueId,
                url: ctx + '/prj/accountabilityfilter/importFilterProjectInfo',
                accept: 'file',
                exts: 'xlsx',
                auto: true,
                data: {
                    accountabilityId: accountabilityId
                },
                done: function (res, index, upload) {
                    // 上传完成后移除临时按钮
                    $('#' + uniqueId).remove();

                    if (res.httpCode == 200) {
                        var successMsg = '导入成功！';
                        if (res.data && res.data !== undefined) {
                            successMsg += '共导入 ' + res.data + ' 条记录。';
                        }
                        layer.msg(successMsg, {icon: 1, time: 3000});

                        setTimeout(function () {
                            //刷新抽查项目表格
                            queryCheckLedgersTableInfo();
                        }, 1200);
                    } else {
                        var errorMsg = res.msg || '导入失败！';
                        if (res.data && res.data.errorDetails) {
                            errorMsg += '\n错误详情：' + res.data.errorDetails;
                        }
                        layer.alert(errorMsg, {
                            icon: 2,
                            title: '导入失败',
                            area: ['600px', '600px'],
                            maxWidth: 800,
                            scrollbar: false
                        });
                    }
                },
                error: function(xhr, status, error) {
                    // 出错时也移除临时按钮
                    $('#' + uniqueId).remove();

                    var errorMsg = '网络异常，导入失败！';
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response && response.msg) {
                            errorMsg = response.msg;
                        }
                    } catch (e) {
                        // 解析失败，使用默认错误信息
                    }
                    layer.msg(errorMsg, {icon: 2});
                }
            });

            // 立即触发文件选择
            $('#' + uniqueId).click();
        }
        /*****************************导入抽查项目***********************************/

        /*****************************附件管理***********************************/
        /**
         * 查询附件列表
         */
        window.queryAttachmentTable = function () {
            $.ajax({
                url: ctx + "/prj/overallAccountabilitynew/queryAccountFileList",
                type: "POST",
                dataType: "json",
                data: JSON.stringify({
                    accountabilityId: accountabilityId
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (ret) {
                    if (ret.httpCode == 200) {
                        renderAttachmentTable(ret.data || []);
                    } else {
                        frm.error(ret.msg || "查询附件列表失败");
                        renderAttachmentTable([]);
                    }
                },
                error: function (e) {
                    frm.error("网络异常，查询附件列表失败");
                    renderAttachmentTable([]);
                }
            });
        };

        /**
         * 渲染附件列表表格
         */
        window.renderAttachmentTable = function (data) {
            table.render({
                elem: '#attachmentTable',
                id: 'attachmentTable',
                data: data,
                page: false,
                even: true,
                cols: [[
                    { type: 'numbers', title: '序号', width: '5%', align: 'center' },
                    { field: 'fileName', title: '附件名称', width:'30%', align: 'left' },
                    {
                        field: 'fileType', title: '附件类型', width: '10%', align: 'center', templet: function (d) {
                            return getAttachmentTypeName(d.fileType);
                        }
                    },
                    {
                        field: 'uploadDate', title: '上传时间', width:'15%', align: 'center', templet: function (d) {
                            return formatDate(d.uploadDate);
                        }
                    },
                    { field: 'createUserName', title: '上传人', width: '10%', align: 'center' },
                    { field: 'provName', title: '上传人省分', width: '10%', align: 'center' },
                    {
                        field: 'submitFlag', title: '是否提交', width: '10%', align: 'center', templet: function (d) {
                            return d.submitFlag == '1' ? '是' : '否';
                        }
                    },
                    { title: '操作', width: '10%', align: 'center', templet: '#attachmentBar', fixed: 'right' }
                ]],
                done: function () {
                    // 表格渲染完成后的回调
                }
            });
        };

        /**
         * 格式化文件大小
         */
        window.formatFileSize = function (bytes) {
            if (bytes === 0) return '0 Bytes';
            var k = 1024;
            var sizes = ['Bytes', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        };

        /**
         * 格式化日期
         */
        window.formatDate = function (dateStr) {
            if (!dateStr) return '';
            var date = new Date(dateStr);
            if (isNaN(date.getTime())) return dateStr;

            var year = date.getFullYear();
            var month = String(date.getMonth() + 1).padStart(2, '0');
            var day = String(date.getDate()).padStart(2, '0');
            var hours = String(date.getHours()).padStart(2, '0');
            var minutes = String(date.getMinutes()).padStart(2, '0');
            var seconds = String(date.getSeconds()).padStart(2, '0');

            return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
        };

        /**
         * 获取附件类型名称（从字典 accountbility_file_type 获取）
         */
        window.getAttachmentTypeName = function (type) {
            // 这里可以从字典中获取，暂时使用硬编码
            var typeMap = {
                '1': '审计报告',
                '2': '审计底稿',
                '3': '相关证明材料',
                '4': '整改报告',
                '5': '其他材料'
            };
            return typeMap[type] || '未知类型';
        };

        /**
         * 上传附件
         */
        window.uploadAttachment = function () {
            // 验证追责主键
            if (!accountabilityId) {
                layer.msg('缺少追责主表主键参数！', { icon: 2 });
                return;
            }

            // 验证附件类型是否选择
            var attachmentType = $("#attachmentType").val();
            if (!attachmentType) {
                layer.msg("请先选择附件类型！", { icon: 2 });

                // 添加视觉提示效果
                var $attachmentType = $("#attachmentType");
                $attachmentType.addClass('attachment-type-required');
                $attachmentType.focus();

                // 3秒后移除提示样式
                setTimeout(function() {
                    $attachmentType.removeClass('attachment-type-required');
                }, 3000);

                return;
            }

            // 每次都重新创建上传组件，确保参数正确
            createAndTriggerAttachmentUpload(attachmentType);
        };

        /**
         * 创建并触发附件上传
         */
        function createAndTriggerAttachmentUpload(attachmentType) {
            // 生成唯一的按钮ID，避免重复使用问题
            var uniqueId = 'uploadAttachmentBtn_' + new Date().getTime();

            // 创建新的隐藏按钮
            var uploadBtn = $('<button style="display: none;" id="' + uniqueId + '"></button>');
            $('body').append(uploadBtn);

            // 创建上传组件
            upload.render({
                elem: '#' + uniqueId,
                url: ctx + '/prj/accountabilityfile/uploadAccountabilityFile',
                accept: 'file',
                exts: 'doc|docx|pdf|xls|xlsx|ppt|pptx|txt|jpg|jpeg|png|gif|zip|rar|7z',
                size: 50 * 1024, // 限制文件大小为50MB
                auto: true,
                data: {
                    businessKey: accountabilityId,
                    fileType: attachmentType
                },
                choose: function (obj) {
                    // 文件选择后的回调
                    var files = obj.pushFile();

                    // 遍历选择的文件
                    obj.preview(function (index, file, result) {
                        var fileName = file.name;
                        var fileSize = file.size;

                        // 验证文件大小（50MB）
                        if (fileSize > 50 * 1024 * 1024) {
                            layer.msg('文件大小不能超过50MB！', { icon: 2 });
                            // 移除临时按钮
                            $('#' + uniqueId).remove();
                            delete files[index];
                            return;
                        }

                        // 确认上传
                        layer.confirm('确定要上传文件"' + fileName + '"吗？<br><span style="color: #666;">文件大小：' + (fileSize / 1024 / 1024).toFixed(2) + ' MB</span><br><span style="color: #666;">附件类型：' + getAttachmentTypeName(attachmentType) + '</span>', {
                            icon: 3,
                            title: '确认上传',
                            area: ['400px', '200px']
                        }, function (confirmIndex) {
                            // 确认上传，显示进度
                            var loadingIndex = layer.msg('正在上传文件，请稍候...', {
                                icon: 16,
                                shade: 0.3,
                                time: 0 // 不自动关闭
                            });
                            layer.close(confirmIndex);
                        }, function () {
                            // 取消上传，移除临时按钮
                            $('#' + uniqueId).remove();
                            delete files[index];
                        });
                    });
                },
                done: function (res, index, upload) {
                    // 上传完成后移除临时按钮
                    $('#' + uniqueId).remove();

                    // 关闭加载层
                    layer.closeAll('loading');
                    layer.closeAll();

                    if (res.httpCode == 200) {
                        var successMsg = "附件上传成功！";
                        if (res.data) {
                            if (res.data.fileName) {
                                successMsg += "<br>文件名：" + res.data.fileName;
                            }
                            if (res.data.fileSize) {
                                successMsg += "<br>文件大小：" + (res.data.fileSize / 1024 / 1024).toFixed(2) + " MB";
                            }
                        }

                        layer.alert(successMsg, {
                            icon: 1,
                            title: '上传成功',
                            area: ['400px', 'auto']
                        });

                        // 刷新附件列表
                        queryAttachmentTable();
                        // 保持附件类型选择，方便用户连续上传同类型附件
                    } else {
                        layer.alert(res.msg || '附件上传失败！', {
                            icon: 2,
                            title: '上传失败',
                            area: ['400px', 'auto']
                        });
                    }
                },
                error: function (index, upload) {
                    // 出错时也移除临时按钮
                    $('#' + uniqueId).remove();

                    // 关闭加载层
                    layer.closeAll('loading');
                    layer.msg('网络异常，附件上传失败！', { icon: 2 });
                }
            });

            // 立即触发文件选择
            $('#' + uniqueId).click();
        }

        /**
         * 下载附件
         */
        window.downloadAttachment = function (attachmentId, fileName) {
            if (!attachmentId) {
                layer.msg('缺少附件ID参数！', { icon: 2 });
                return;
            }

            // 显示下载确认
            var confirmMsg = '确定要下载该附件吗？';
            if (fileName) {
                confirmMsg = '确定要下载附件"' + fileName + '"吗？';
            }

            layer.confirm(confirmMsg, {
                icon: 3,
                title: '确认下载',
                area: ['350px', '150px']
            }, function (index) {
                // 显示下载提示
                var loadingIndex = layer.msg('正在准备下载，请稍候...', {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });

                // 使用新的下载接口
                var downloadUrl = ctx + '/files/downLoad/' + attachmentId;

                // 先检查文件是否存在
                $.ajax({
                    url: downloadUrl,
                    type: 'HEAD',
                    success: function () {
                        // 文件存在，开始下载
                        var link = document.createElement('a');
                        link.href = downloadUrl;
                        link.download = fileName || '附件文件';
                        link.style.display = 'none';

                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        layer.close(loadingIndex);
                        layer.msg('下载已开始！', { icon: 1 });
                    },
                    error: function (xhr) {
                        layer.close(loadingIndex);

                        if (xhr.status === 404) {
                            layer.msg('文件不存在或已被删除！', { icon: 2 });
                        } else {
                            layer.msg('下载失败，请稍后重试！', { icon: 2 });
                        }
                    }
                });

                // 关闭确认框
                layer.close(index);
            });
        };

        /**
         * 删除附件
         */
        window.deleteAttachment = function (attachmentId, fileName) {
            if (!attachmentId) {
                layer.msg('缺少附件ID参数！', { icon: 2 });
                return;
            }

            var confirmMsg = '确定要删除该附件吗？';
            if (fileName) {
                confirmMsg = '确定要删除附件"' + fileName + '"吗？';
            }

            layer.confirm(confirmMsg + '<br><span style="color: #ff5722;">注意：删除后无法恢复！</span>', {
                icon: 3,
                title: '确认删除',
                area: ['400px', '180px']
            }, function (index) {
                // 显示删除进度
                var loadingIndex = layer.msg('正在删除，请稍候...', {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });

                $.ajax({
                    url: ctx + "/prj/accountabilityfile/deleteAccountabilityFile/" +attachmentId ,
                    type: "post",
                    dataType: "json",
                    contentType: 'application/json;charset=UTF-8',
                    data: JSON.stringify({
                        attachmentId: attachmentId
                    }),
                    success: function (ret) {
                        layer.close(loadingIndex);

                        if (ret.httpCode == 200) {
                            layer.msg("附件删除成功！", { icon: 1 });
                            // 刷新附件列表
                            queryAttachmentTable();
                        } else {
                            layer.msg(ret.msg || '删除失败！', { icon: 2 });
                        }
                    },
                    error: function (xhr, status, error) {
                        layer.close(loadingIndex);

                        var errorMsg = '网络异常，删除失败！';
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response && response.msg) {
                                errorMsg = response.msg;
                            }
                        } catch (e) {
                            // 解析失败，使用默认错误信息
                        }

                        layer.msg(errorMsg, { icon: 2 });
                    }
                });

                // 关闭确认框
                layer.close(index);
            });
        };

        /**
         * 批量删除附件
         */
        window.batchDeleteAttachments = function () {
            // 获取选中的行数据
            var checkStatus = table.checkStatus('attachmentTable');
            var data = checkStatus.data;

            if (data.length === 0) {
                layer.msg('请至少选择一个附件！', { icon: 2 });
                return;
            }

            // 获取选中的附件ID列表
            var attachmentIds = [];
            var fileNames = [];
            $.each(data, function (index, item) {
                if (item.attachmentId) {
                    attachmentIds.push(item.attachmentId);
                    fileNames.push(item.fileName || '未知文件');
                }
            });

            if (attachmentIds.length === 0) {
                layer.msg('获取选中附件ID失败！', { icon: 2 });
                return;
            }

            var confirmMsg = '确定要删除选中的 ' + data.length + ' 个附件吗？';
            if (fileNames.length > 0) {
                confirmMsg += '<br><span style="color: #666;">包括：' + fileNames.slice(0, 3).join('、');
                if (fileNames.length > 3) {
                    confirmMsg += ' 等';
                }
                confirmMsg += '</span>';
            }
            confirmMsg += '<br><span style="color: #ff5722;">注意：删除后无法恢复！</span>';

            layer.confirm(confirmMsg, {
                icon: 3,
                title: '确认批量删除',
                area: ['450px', '220px']
            }, function (index) {
                // 显示删除进度
                var loadingIndex = layer.msg('正在批量删除，请稍候...', {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });

                // 批量删除（逐个调用删除接口）
                var deletePromises = [];
                $.each(attachmentIds, function (i, attachmentId) {
                    var promise = $.ajax({
                        url: ctx + "/prj/accountabilityfile/deleteAccountabilityFile/" + attachmentId,
                        type: "DELETE",
                        dataType: "json"
                    });
                    deletePromises.push(promise);
                });

                // 等待所有删除操作完成
                $.when.apply($, deletePromises).done(function () {
                    layer.close(loadingIndex);
                    layer.msg('批量删除成功！', { icon: 1 });
                    // 刷新附件列表
                    queryAttachmentTable();
                }).fail(function () {
                    layer.close(loadingIndex);
                    layer.msg('部分附件删除失败，请检查后重试！', { icon: 2 });
                    // 刷新附件列表
                    queryAttachmentTable();
                });

                // 关闭确认框
                layer.close(index);
            });
        };


        /**
         * 根据追责状态控制字段的可编辑性
         */
        function controlFieldsByStatus() {
            console.log('当前追责状态:', accountabilityStatus);

            if (accountabilityStatus == '2') {
                // 追责中状态：显示整改状态字段，基本信息字段不可编辑
                $("#reformStatusDiv").show();

                // 设置基本信息字段为只读（全部使用 readonly，不使用 disabled）
                $("#checkAuditName").attr('readonly', true).addClass('readonly-field');
                $("#auditPeriod").attr('readonly', true).addClass('readonly-field');
                $("#checkMonth").attr('readonly', true).addClass('readonly-field');
                $("#auditTeam").attr('readonly', true).addClass('readonly-field');

                // 复选框也设置为只读状态（通过CSS和事件阻止）
                $("#isSubCheck").attr('readonly', true).addClass('readonly-field');
                // 阻止复选框的点击事件
                $("#isSubCheck").off('click').on('click', function(e) {
                    e.preventDefault();
                    return false;
                });

                // 隐藏选择按钮
                $("button[onclick='queryUser()']").hide();

                // 隐藏抽查项目相关的操作按钮
                $("#projectFilterBtn").hide(); // 项目筛选按钮
                $("button[onclick='exportProject()']").hide(); // 导出抽查项目按钮
                $("button[onclick='importProject()']").hide(); // 导入抽查项目按钮
                $("#batchDeleteBtn").hide(); // 批量删除按钮

                // 隐藏项目台账相关的操作按钮
                $(".btn-generate-accountability").hide(); // 生成整体追责按钮



                // 禁用表格中的删除操作按钮
                setTimeout(function() {
                    $(".table-btn[onclick*='deleteSingleItem']").each(function() {
                        $(this).css({
                            'opacity': '0.5',
                            'cursor': 'not-allowed',
                            'pointer-events': 'none'
                        }).attr('title', '追责中状态不允许删除');
                    });

                    // 禁用附件删除按钮
                    $(".table-btn[onclick*='deleteAttachment']").each(function() {
                        $(this).css({
                            'opacity': '0.5',
                            'cursor': 'not-allowed',
                            'pointer-events': 'none'
                        }).attr('title', '追责中状态不允许删除');
                    });
                }, 100);

                // 重新初始化日期选择器（禁用状态）
                if (typeof initDatePickers === 'function') {
                    initDatePickers();
                }

                // 修改页面标题
                $(".form-bg-box").prepend('<div class="status-notice" style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin-bottom: 15px; border-radius: 4px; color: #856404;"><i class="iconfont" style="color: #f39c12;">&#xe63a;</i> 当前状态：追责中，基本信息不可修改，只能更新整改状态</div>');

            } else if (accountabilityStatus == '0' || !accountabilityStatus) {
                // 草稿状态或新增：隐藏整改状态字段，所有字段可编辑
                $("#reformStatusDiv").hide();

                // 确保基本信息字段可编辑（除了本来就只读的字段）
                $("#checkAuditName").removeAttr('readonly').removeClass('readonly-field');
                $("#auditTeam").removeAttr('readonly').removeClass('readonly-field');
                $("#checkMonth").removeAttr('readonly').removeClass('readonly-field');
                $("#auditPeriod").removeAttr('readonly').removeClass('readonly-field');

                // 恢复复选框的正常功能
                $("#isSubCheck").removeAttr('readonly').removeClass('readonly-field');
                $("#isSubCheck").off('click'); // 移除阻止点击的事件

                // 重新初始化日期选择器（启用状态）
                if (typeof initDatePickers === 'function') {
                    initDatePickers();
                }

                // 显示选择按钮
                $("button[onclick='queryUser()']").show();

                // 显示抽查项目相关的操作按钮
                $("#projectFilterBtn").show(); // 项目筛选按钮
                $("button[onclick='exportProject()']").show(); // 导出抽查项目按钮
                $("button[onclick='importProject()']").show(); // 导入抽查项目按钮
                $("#batchDeleteBtn").show(); // 批量删除按钮

                // 显示项目台账相关的操作按钮
                $(".btn-generate-accountability").show(); // 生成整体追责按钮

                // 显示附件相关的操作按钮
                $("#uploadAttachmentBtnMain").show(); // 上传附件按钮

                // 恢复表格中的删除操作按钮
                setTimeout(function() {
                    $(".table-btn[onclick*='deleteSingleItem']").each(function() {
                        $(this).css({
                            'opacity': '1',
                            'cursor': 'pointer',
                            'pointer-events': 'auto'
                        }).attr('title', '删除');
                    });

                    // 恢复附件删除按钮
                    $(".table-btn[onclick*='deleteAttachment']").each(function() {
                        $(this).css({
                            'opacity': '1',
                            'cursor': 'pointer',
                            'pointer-events': 'auto'
                        }).attr('title', '删除');
                    });
                }, 100);
            }
        }

        // 页面初始化
        $(document).ready(function () {
            // 监听附件类型选择变化，移除错误提示样式
            $("#attachmentType").on('change', function () {
                $(this).removeClass('attachment-type-required');
            });

            // 监听基本信息字段变化，移除错误提示样式
            $("#checkAuditName, #checkMonth, #auditPeriod, #auditTeam").on('input change', function () {
                $(this).removeClass('field-required');
            });

            // 根据追责状态控制字段可编辑性
            controlFieldsByStatus();

        });

        /*****************************附件管理***********************************/

        /*****************************项目台账***********************************/

        /*****************************项目台账***********************************/

        // 存储组织方式字典数据
        var auditTypeDictionary = {};

        // 存储整改状态字典数据
        var reformStatusDictionary = {};

        // 加载组织方式字典数据
        window.loadAuditTypeDictionary = function () {
            $.ajax({
                url: ctx + "/index/querySysDicByType",
                dataType: "JSON",
                type: "POST",
                data: {
                    type: "auditType"
                },
                success: function (ret) {
                    if (ret && ret.data) {
                        // 将字典数据存储到全局变量中，用于回显
                        $.each(ret.data, function (index, item) {
                            auditTypeDictionary[item.code] = item.codeText;
                        });
                    }
                },
                error: function (e) {
                    console.info("加载组织方式字典失败:", e);
                }
            });
        };

        // 加载整改状态字典数据（前端写死）
        window.loadReformStatusDictionary = function () {
            // 前端写死的整改状态字典数据
            var reformStatusData = [
                {code: '0', codeText: '未整改'},
                {code: '1', codeText: '整改中'},
                {code: '2', codeText: '整改完成'}
            ];

            // 使用模板渲染整改状态选项
            var getTpl = $('#reformStatusTemplate').html();
            tpl(getTpl).render(reformStatusData, function (html) {
                $("#reformStatus").html(html);
                form.render('select'); // 重新渲染select组件
            });

            // 将字典数据存储到全局变量中，用于回显
            $.each(reformStatusData, function (index, item) {
                reformStatusDictionary[item.code] = item.codeText;
            });
        };

        // 根据auditType值设置组织方式显示名称
        window.setAuditTypeName = function (auditTypeValue) {
            if ((auditTypeValue != '' || auditTypeValue != undefined) && auditTypeDictionary[auditTypeValue]) {
                $("#auditType").val(auditTypeValue);
                $("#auditTypeName").val(auditTypeDictionary[auditTypeValue]);
            }
        };

        //查询初始化数据
        queryAccountabilityInfo();
        //加载组织方式字典
        loadAuditTypeDictionary();
        //加载整改状态字典
        loadReformStatusDictionary();
        // 注意：抽查项目表格和项目台账的加载由queryAccountabilityInfo中控制

        // 监听"是否对下检查"复选框变化
        form.on('switch(isSubCheck)', function (data) {
            // 在追责中状态时，不响应复选框变化
            if (accountabilityStatus == '2') {
                console.log('追责中状态，忽略复选框变化');
                return false;
            }
            // 当复选框状态改变时，重新加载抽查项目表格
            queryCheckLedgersTableInfo();
        });

        // 添加必填字段实时验证
        addRequiredFieldValidation();

        // 监听日期字段变化（由于使用了laydate，需要在change回调中更新状态）
        // 这些监听器已经在laydate.render的change回调中处理
    });



</script>

</html>
