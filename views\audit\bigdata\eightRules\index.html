<!--八规模型-->
<!--#include virtual ="include/header.html"-->
<link href="resource/css/audit/online/tagmodel/tagIndex.css?v=6.5" rel="stylesheet" type="text/css">
<link href="resource/css/audit/online/tagmodel/tagCommon.css?v=6.5" rel="stylesheet" type="text/css">
<style>
    .layui-laydate .laydate-btns-clear {
        display: none;
    }

    .layui-table-view .layui-table {
        min-width: 100%;
    }

    .new-table .layui-table-view .layui-table td, .new-table .layui-table-view .layui-table th {
        border-left: 1px solid #ddd;
        border-right: 1px solid #ddd;
    }

    .new-table .layui-table-cell {
        min-width: 100%;
    }

    .layui-common-body {
        background: #fff;
    }

    .layui-form-item {
        margin-bottom: 10px;
    }

    .tab_2 .new-style .layui-input-block {
        margin-left: 177px;
    }

    .tab_2 .new-style .layui-form-label {
        width: 175px;
    }

    .new-style .layui-input-block {
        margin-left: 50px;
    }

    .new-style .layui-form-label {
        width: 50px;
    }

    .layui-search-new.new-style .layui-form-label {
        padding: 0 6px 0 0;
    }

    .ict-header-list {
        display: inline-block;
        width: 100%;
        text-align: left;
    }

    .ict-header-li {
        padding: 0 26px;
        box-sizing: border-box;
        display: inline-block;
        cursor: pointer;
    }

    .ict-header-title {
        font-family: PingFangSC, PingFang SC;
        font-size: 15px;
        color: #333333;
        font-style: normal;
        border-bottom: 3px solid #fff;
        height: 42px;
    }

    .ict-header-li.active .ict-header-title {
        color: #C20000;
        border-bottom: 3px solid #C20000;
    }

    .ict-search-box {
        width: 100%;
        margin-top: 8px;
        background: #F7F9FA;
        padding: 10px 10px 0 10px;
        box-sizing: border-box;
    }

    .ict-search-form {
        width: 100%;
        float: left;
    }

    .ict-search-btn {
        width: 100px;
        float: right;
    }

    .model-btn-submit {
        margin-bottom: 10px;
    }

    .search-input input {
        height: 42px;
        width: 450px;
    }

    .search-input {
        margin-top: 15px;
    }

    .search-input .search-btn-right {
        width: 100px;
        height: 42px;
        border-radius: 0 4px 4px 0;
        background: #C20000;
    }

    .model-btn.model-btn-submit {
        background-color: #C20000;
        border: 1px solid #C20000;
    }

    .search-input .search-btn-right.search-btn-right-2 {
        margin-left: 20px;
        width: 120px;
        border-radius: 4px;
    }

    .search-input .search-btn-right.search-btn-right-2 .search-btn-right-text {
        padding: 0;
    }

    .new-style .layui-input-block .input-p {
        padding: 4px 0;
        display: inline-block;
        box-sizing: border-box;
    }

    .formButton {
        text-align: right;
    }

    .formButton button {
        height: 32px;
        line-height: 32px;
    }

    .layui-input[disabled="disabled"] {
        background: #eee !important;
    }


    .goClick {
        color: #C20000;
        cursor: pointer;
    }

    .dw-tip {
        text-align: right;
        color: #333333;
    }

    .model-btn.model-btn-export {
        min-width: 88px;
        padding: 0 12px;
        box-sizing: border-box;
        background-color: #ffffff;
        border: 1px solid #c20000;
        border-radius: 2px;
    }

    .model-btn.model-btn-export span {
        color: #c20000;
    }

    .model-btn.model-btn-export i {
        color: #c20000;
    }

    .layui-card-header-select.active:before {
        width: 100%;
        height: 2px;
        left: 0;
    }

    .layui-card-header-select {
        font-weight: inherit;
    }
    .layui-table-cell {
        height: auto;
        padding: 0 8px;
        line-height: 20px;
        position: relative;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        box-sizing: border-box;
    }
</style>
<body>
<div class="layui-common-body layui-row">


    <div class="layui-row layui-col-md12 layui-col-sm12 layui-col-lg12">
        <div class="layui-row">
            <div class="layui-common-box">
                <div class="layui-row layui-common-card">
                    <div class="layui-card-body main-list-body layui-row" style="padding-top: 4px;">
                        <div class="ict-search-box layui-row">
                            <div class="ict-search-form layui-form new-style layui-row">
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">账期</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input" readonly id="monthId" type="text">
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="layui-form-item layui-form-item-sm">
                                        <label class="layui-form-label color">省分</label>
                                        <div class="layui-input-block">
                                            <select id="provCode" lay-filter="provCode" name="provCode">
                                                <option selected value="">--请选择--</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3" style="min-height: 10px;">
                                    <div class="layui-form-item layui-form-item-sm" style="display: none" id="areaBox">
                                        <label class="layui-form-label color">地市</label>
                                        <div class="layui-input-block">
                                            <select id="areaCode" lay-filter="areaCode" name="areaCode">
                                                <option selected value="">--请选择--</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="float-left layui-col-md3 layui-col-sm3 layui-col-lg3"
                                     style="text-align: right;">
                                    <div class="model-btn model-btn-submit" onclick="tableLoadFun()" title="查询">
                                        <i class="iconfont search-icon">&#xe60b;</i>
                                        <span>查询</span>
                                    </div>
                                    <div class="model-btn model-btn-reset" onclick="restFun()" title="重置">
                                        <i class="iconfont search-icon">&#xe63a;</i>
                                        <span>重置</span>
                                    </div>
                                    <div class="model-btn  model-btn-export" onclick="exportBtn()" title="导出">
                                        <i class="iconfont search-icon">&#xe60c;</i>
                                        <span>导出</span>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <div class="tab_1 tab_box">
                            <div class="layui-common-header" style="padding: 0;">
                                <div class="layui-common-card-headers">
                                    <div class="layui-card-header-select active" data-code="1">省分汇总</div>
                                    <div class="layui-card-header-select" data-code="2">地市汇总</div>
                                    <div class="layui-card-header-select" data-code="3">明细详情</div>
                                </div>
                            </div>
                            <div class="layui-card-body main-list-body" style="padding:0;">
                                <div class="work-trend-box new-table">
                                    <table class="layui-table jq-even" id="table_1"
                                           lay-filter="table_1"></table>
                                </div>
                            </div>

                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>

<!--#include virtual ="include/version.html"-->
<script src="resource/js/jquery/jquery.js" type="text/javascript"></script>
<!--省分-->
<script id="selectTpl2" type="text/html">
    <option value="">--请选择--</option>
    {{# layui.each(d, function(index, item){ }}
    <option value="{{item.CODE}}">{{item.CODETEXT}}</option>
    {{# }); }}
</script>
<script>

    var loginName = loadCurUser().loginName;
    layui.use(
        [
            "jqform",
            "table",
            "laytpl",
            "jquery",
            "laydate",
            "layer",
            "jqbind",
            "upload",
            "jqztree",
            "laydate",
            "laypage",
        ],
        function () {
            var $ = layui.jquery,
                ctx = top.global.ctx,
                $ZTree = layui.jqztree,
                layer = layui.layer,
                form = layui.jqform,
                tpl = layui.laytpl,
                laydate = layui.laydate,
                table = layui.table,
                upload = layui.upload,
                laypage = layui.laypage,
                jqbind = layui.jqbind;
            jqbind.init();
            tagIndex = '1';
            monthIdOld = '';//初始化的有值账期
            drillingFlag = '';//钻取的值
            var page = 1;
            var limit = 10;
            var sortStates = {};//排序

            //导出
            window.exportBtn = function () {
                var monthId = $("#monthId").val();
                var startMonthId = monthId.split(' - ')[0];
                var endMonthId = monthId.split(' - ')[1] ? monthId.split(' - ')[1] : '';
                var provCode = $('#provCode').val();
                var areaCode = $('#areaCode').val();
                var statType = '1'
                var aggregationType = '2'
                var url;
                var sortList = [];
                if(sortStates){
                    sortList.push(sortStates)
                }



                if(tagIndex=='1'){
                     statType = '1'
                     url = ctx + "/rules-invoice/export?startMonthId=" + startMonthId + '&endMonthId=' + endMonthId +
                        '&statType=' +statType + '&provCode=' +provCode+'&areaCode='+areaCode+'&aggregationType='+aggregationType+'&page=' +page+ '&limit=' +limit
                     +'&sortList='+  encodeURIComponent(JSON.stringify(sortList));
                }else if(tagIndex=='2'){
                    statType = '2'
                    url = ctx + "/rules-invoice/export?startMonthId=" + startMonthId + '&endMonthId=' + endMonthId +
                        '&statType=' +statType + '&provCode=' +provCode+'&areaCode='+areaCode+'&aggregationType='+aggregationType+'&page=' +page+ '&limit=' +limit+'&sortList='+ encodeURIComponent(JSON.stringify(sortList));
                }else if(tagIndex=='3'){
                    url = ctx + "/rules-invoice/info/export?" +
                        "startMonthId=" + startMonthId
                        + '&endMonthId=' + endMonthId
                        +'&statType=' +statType
                        + '&provCode=' +provCode
                        +'&areaCode='+areaCode
                        +'&page=' +page
                        +'&limit=' +limit
                        +'&sortList='+ encodeURIComponent(JSON.stringify(sortList));

                    ;
                }

               window.location.href = url;
                return false;
            };

            // tab切换
            $('.layui-card-header-select').click(function () {
                var index = $(this).data('code');
                if (index == tagIndex) {
                    return false;
                }
                if(index=='1'){
                    $('#areaBox').hide()
                }else{
                    $('#areaBox').show()
                }
                $('#provCode').val('')
                $('#areaCode').val('')
                lp(tpl, [], selectTpl2.innerHTML, $('#areaCode'));
                tagIndex = index;
                $('.layui-card-header-select').removeClass('active');
                $(this).addClass('active');
                drillingFlag = ''
                form.render();
                switch (index) {
                    case 1: {
                        tableLoad()
                        break;
                    }
                    case 2: {
                        tableLoad1()
                        break;
                    }
                    case 3: {
                        tableLoad2()
                        break;
                    }

                }
            });

            // 账期
            laydate.render({
                elem: '#monthId'
                , type: 'month'
                // , value:'202001 - 202412'
                , range: '-'
                , format: 'yyyyMM'
            });

            //省分
            window.provinceList = function () {
                //省分
                $.ajax({
                    url: ctx + '/prj/timeLimit/auditSendProv',
                    type: "POST",
                    dataType: "json",
                    data: JSON.stringify({}),
                    contentType: "application/json;charset=UTF-8",
                    success: function (ret) {
                        lp(tpl, ret, selectTpl2.innerHTML, $('#provCode'));
                        form.render();
                    },
                    error: function (e) {
                    }
                });
            }
            provinceList()

            //地市
            window.getAuditSendArea = function (type, areaCode) {
                var orgId = $('#provCode').val();
                //地市
                $.ajax({
                    url: ctx + "/prj/timeLimit/getAudtiSendArea/" + orgId,
                    type: "POST",
                    dataType: "json",
                    data: JSON.stringify({}),
                    contentType: "application/json;charset=UTF-8",
                    success: function (ret) {
                        lp(tpl, ret, selectTpl2.innerHTML, $('#areaCode'));
                        form.render();
                        if (type == 'drilling') {
                            $('#areaCode').val(areaCode)
                            form.render()
                            tableLoad2()
                        }
                    },
                    error: function (e) {
                    }
                });
            }

            // 省分下拉框触发
            form.on('select(provCode)', function (data) {
                getAuditSendArea();
            });
            //省份汇总
            window.tableLoad = function (type) {
                var monthId = $("#monthId").val();
                var startMonthId = monthId.split(' - ')[0];
                var endMonthId = monthId.split(' - ')[1] ? monthId.split(' - ')[1] : '';
                var statType = '1'
                var provCode = $('#provCode').val();
                var areaCode = $('#areaCode').val();
                var aggregationType = '2'
                var sortList = []
                sortStates = {}
                table.render({
                    elem: "#table_1",
                    even: true,
                    url: ctx + "/rules-invoice/getPage",
                    where: {
                        startMonthId: startMonthId,
                        endMonthId: endMonthId,
                        statType: statType,
                        provCode: provCode,
                        areaCode: areaCode,
                        aggregationType: aggregationType,
                        sortList:sortList
                    },
                    isClient: false,
                    page: true,
                    cols: [
                        [
                            {type: "numbers", title: "序号", align: "center", width: 80},
                            {field: "monthId", title: "账期", align: "center", width: 155},
                            {field: "provName", title: "省分",sort: true, align: "center", width: 100,templet: function (d) {
                                    if (d.provName) {
                                        return '<p class="cursor text-red underline"  onclick="jumpPage(\'provName\', \'' + d.provCode + '\')" lay-event="jumpPage"  title="' + d.provName + '"   >' + d.provName + '</p>';
                                    } else {
                                        return ''
                                    }
                                }},
                            {field: "entPropRev", title: "招待费占收比",sort: true, align: "center", width: 120},
                            {
                                field: "issueInvTag",
                                title: "成立即开票",
                                sort: true,
                                align: "center",
                                width: 110,
                                templet: function (d) {
                                    if (d.issueInvTag != 0) {
                                        return '<p class="cursor text-red underline" onclick="jumpPage(\'issueInvTag\', \'' + d.provCode + '\')" title="' + d.issueInvTag + '">' + d.issueInvTag + '</p>';
                                    } else {
                                        return d.issueInvTag
                                    }
                                }
                            },
                            {
                                field: "sameDayStoreInvTag",
                                title: "同日同店开票",
                                sort: true,
                                align: "center",
                                width: 120,
                                templet: function (d) {
                                    if (d.sameDayStoreInvTag != 0) {
                                        return '<p class="cursor text-red underline"  onclick="jumpPage(\'sameDayStoreInvTag\', \'' + d.provCode + '\')" lay-event="jumpPage"  title="' + d.sameDayStoreInvTag + '"   >' + d.sameDayStoreInvTag + '</p>';
                                    } else {
                                        return d.sameDayStoreInvTag
                                    }
                                }
                            },
                            {
                                field: "sameAddrInvTag",
                                title: "不同店注册地址相同",
                                sort: true,
                                align: "center",
                                width: 160,
                                templet: function (d) {
                                    if (d.sameAddrInvTag != 0) {
                                        return '<p class="cursor text-red underline" onclick="jumpPage(\'sameAddrInvTag\', \'' + d.provCode + '\')" lay-event="jumpPage"  title="' + d.sameAddrInvTag + '"   >' + d.sameAddrInvTag + '</p>';
                                    } else {
                                        return d.sameAddrInvTag
                                    }
                                }
                            },
                            {
                                field: "diffPlaceInvTag",
                                title: "异地开票",
                                sort: true,
                                align: "center",
                                width: 100,
                                templet: function (d) {

                                    if (d.diffPlaceInvTag != 0) {
                                        return '<p class="cursor text-red underline" onclick="jumpPage(\'diffPlaceInvTag\', \'' + d.provCode + '\')" lay-event="jumpPage"  title="' + d.diffPlaceInvTag + '"   >' + d.diffPlaceInvTag + '</p>';
                                    } else {
                                        return d.diffPlaceInvTag
                                    }
                                }
                            },
                            {
                                field: "unreasonableInvTag",
                                title: "事由存在不合理",
                                sort: true,
                                align: "center",
                                width: 130,
                                templet: function (d) {
                                    if (d.unreasonableInvTag != 0) {
                                        return '<p class="cursor text-red underline" onclick="jumpPage(\'unreasonableInvTag\', \'' + d.provCode + '\')" lay-event="jumpPage"  title="' + d.unreasonableInvTag + '"   >' + d.unreasonableInvTag + '</p>';
                                    } else {
                                        return d.unreasonableInvTag
                                    }
                                }
                            },
                            {
                                field: "marketInvTag",
                                title: "店名含贸易零售",
                                sort: true,
                                align: "center",
                                width: 130,
                                templet: function (d) {
                                    if (d.marketInvTag != 0) {
                                        return '<p class="cursor text-red underline" onclick="jumpPage(\'marketInvTag\', \'' + d.provCode + '\')" lay-event="jumpPage"  title="' + d.marketInvTag + '"   >' + d.marketInvTag + '</p>';
                                    } else {
                                        return d.marketInvTag
                                    }
                                }
                            },
                            {
                                field: "sameStaffInvTag",
                                title: "相同报账人同日多店开票",
                                sort: true,
                                align: "center",
                                width: 190,
                                templet: function (d) {
                                    if (d.sameStaffInvTag != 0) {
                                        return '<p class="cursor text-red underline" onclick="jumpPage(\'sameStaffInvTag\', \'' + d.provCode + '\')" lay-event="jumpPage"  title="' + d.sameStaffInvTag + '"   >' + d.sameStaffInvTag + '</p>';
                                    } else {
                                        return d.sameStaffInvTag
                                    }
                                }
                            }
                        ]
                    ],

                    done: function (res, curr, count) {
                        page = res.page;
                        limit = res.limit;
                        var data = res.data;
                        if (data.length > 0 && type == 1) {
                            //取第一条的账期
                            monthIdOld = data[0].monthId.split('-')[0] + ' - ' + data[0].monthId.split('-')[1];
                            $('#monthId').val(monthIdOld)
                        }
                    },
                });
            };

            //地市汇总
            window.tableLoad1 = function () {
                var monthId = $("#monthId").val();
                var startMonthId = monthId.split(' - ')[0];
                var endMonthId = monthId.split(' - ')[1] ? monthId.split(' - ')[1] : '';
                var statType = '2'
                var provCode = $('#provCode').val();
                var areaCode = $('#areaCode').val();
                var aggregationType = '2'
                var sortList = []
                sortStates = {}
                table.render({
                    elem: "#table_1",
                    even: true,
                    url: ctx + "/rules-invoice/getPage",
                    where: {
                        startMonthId: startMonthId,
                        endMonthId: endMonthId,
                        statType: statType,
                        provCode: provCode,
                        areaCode: areaCode,
                        aggregationType: aggregationType,
                        sortList:sortList
                    },
                    isClient: false,
                    page: true,
                    cols: [
                        [
                            {type: "numbers", title: "序号", align: "center", width: 80},
                            {field: "monthId", title: "账期", align: "center", width: 155},
                            {field: "provName", title: "省分",sort: true, align: "center", width: 100},
                            {field: "areaName", title: "地市",sort: true, align: "center", width: 100,templet: function (d) {
                                    if (d.provName) {
                                        return '<p class="cursor text-red underline"  onclick="jumpPage(\'\', \'' + d.provCode + '\', \'' + d.areaCode + '\')" lay-event="jumpPage"  title="' + d.areaName + '"   >' + d.areaName + '</p>';
                                    } else {
                                        return ''
                                    }
                                }},
                            {field: "entPropRev", title: "招待费占收比",sort: true, align: "center", width: 120},
                            {
                                field: "issueInvTag",
                                title: "成立即开票",
                                sort: true,
                                align: "center",
                                width: 110,
                                templet: function (d) {
                                    if (d.issueInvTag != 0) {
                                        return '<p class="cursor text-red underline" onclick="jumpPage(\'issueInvTag\', \'' + d.provCode + '\', \'' + d.areaCode + '\')" title="' + d.issueInvTag + '">' + d.issueInvTag + '</p>';
                                    } else {
                                        return d.issueInvTag
                                    }
                                }
                            },
                            {
                                field: "sameDayStoreInvTag",
                                title: "同日同店开票",
                                sort: true,
                                align: "center",
                                width: 120,
                                templet: function (d) {
                                    if (d.sameDayStoreInvTag != 0) {
                                        return '<p class="cursor text-red underline"  onclick="jumpPage(\'sameDayStoreInvTag\', \'' + d.provCode + '\', \'' + d.areaCode + '\')" lay-event="jumpPage"  title="' + d.sameDayStoreInvTag + '"   >' + d.sameDayStoreInvTag + '</p>';
                                    } else {
                                        return d.sameDayStoreInvTag
                                    }
                                }
                            },
                            {
                                field: "sameAddrInvTag",
                                title: "不同店注册地址相同",
                                sort: true,
                                align: "center",
                                width: 160,
                                templet: function (d) {
                                    if (d.sameAddrInvTag != 0) {
                                        return '<p class="cursor text-red underline" onclick="jumpPage(\'sameAddrInvTag\', \'' + d.provCode + '\', \'' + d.areaCode + '\')" lay-event="jumpPage"  title="' + d.sameAddrInvTag + '"   >' + d.sameAddrInvTag + '</p>';
                                    } else {
                                        return d.sameAddrInvTag
                                    }
                                }
                            },
                            {
                                field: "diffPlaceInvTag",
                                title: "异地开票",
                                sort: true,
                                align: "center",
                                width: 100,
                                templet: function (d) {

                                    if (d.diffPlaceInvTag != 0) {
                                        return '<p class="cursor text-red underline" onclick="jumpPage(\'diffPlaceInvTag\', \'' + d.provCode + '\', \'' + d.areaCode + '\')" lay-event="jumpPage"  title="' + d.diffPlaceInvTag + '"   >' + d.diffPlaceInvTag + '</p>';
                                    } else {
                                        return d.diffPlaceInvTag
                                    }
                                }
                            },
                            {
                                field: "unreasonableInvTag",
                                title: "事由存在不合理",
                                sort: true,
                                align: "center",
                                width: 130,
                                templet: function (d) {
                                    if (d.unreasonableInvTag != 0) {
                                        return '<p class="cursor text-red underline" onclick="jumpPage(\'unreasonableInvTag\', \'' + d.provCode + '\', \'' + d.areaCode + '\')" lay-event="jumpPage"  title="' + d.unreasonableInvTag + '"   >' + d.unreasonableInvTag + '</p>';
                                    } else {
                                        return d.unreasonableInvTag
                                    }
                                }
                            },
                            {
                                field: "marketInvTag",
                                title: "店名含贸易零售",
                                sort: true,
                                align: "center",
                                width: 130,
                                templet: function (d) {
                                    if (d.marketInvTag != 0) {
                                        return '<p class="cursor text-red underline" onclick="jumpPage(\'marketInvTag\', \'' + d.provCode + '\', \'' + d.areaCode + '\')" lay-event="jumpPage"  title="' + d.marketInvTag + '"   >' + d.marketInvTag + '</p>';
                                    } else {
                                        return d.marketInvTag
                                    }
                                }
                            },
                            {
                                field: "sameStaffInvTag",
                                title: "相同报账人同日多店开票",
                                sort: true,
                                align: "center",
                                width: 190,
                                templet: function (d) {
                                    if (d.sameStaffInvTag != 0) {
                                        return '<p class="cursor text-red underline" onclick="jumpPage(\'sameStaffInvTag\', \'' + d.provCode + '\', \'' + d.areaCode + '\')" lay-event="jumpPage"  title="' + d.sameStaffInvTag + '"   >' + d.sameStaffInvTag + '</p>';
                                    } else {
                                        return d.sameStaffInvTag
                                    }
                                }
                            }
                        ]
                    ],

                    done: function (res, curr, count) {
                        page = res.page;
                        limit = res.limit;

                    },
                })
            };

            //明细详情
            window.tableLoad2 = function () {
                var monthId = $("#monthId").val();
                var startMonthId = monthId.split(' - ')[0];
                var endMonthId = monthId.split(' - ')[1] ? monthId.split(' - ')[1] : '';
                var provCode = $('#provCode').val();
                var areaCode = $('#areaCode').val();



                var sortList = []
                if (drillingFlag!='') {
                    sortList.push({sortKey:drillingFlag,sortValue:true})
                    sortStates = {sortKey:drillingFlag,sortValue:true}
                }
                table.render({
                    elem: "#table_1",
                    id: 'table_1',
                    even: true,
                    url: ctx + "/rules-invoice/info/getPage",
                    where: {
                        startMonthId: startMonthId,
                        endMonthId: endMonthId,
                        provCode: provCode,
                        areaCode: areaCode,
                        sortList:sortList
                    },
                    isClient: false,
                    page: true,
                    cols: [
                        [
                            {type: "numbers", title: "序号", align: "center", width: 60,fixed: "left"},
                            {field: "monthId", title: "账期", align: "center", width: 75},
                            {field: "provName", title: "省分",sort: true, align: "center", width: 90},
                            {field: "areaName", title: "地市",sort: true, align: "center", width: 90},
                            {
                                field: "payerBelongDeptNm",
                                title: "所属部门",
                                align: "center",
                                width: 260,sort: true,
                                style: 'text-align:left;'
                            },
                            {field: "srcDocMakerNm", title: "来源单据起草人",sort: true, align: "center", width: 133},
                            {field: "payeeAcctNm", title: "收方账户名称",sort: true, align: "center", width: 120},
                            {
                                field: "payReason",
                                title: "付款事由",
                                sort: true,
                                align: "center",
                                width: 260,
                                style: 'text-align:left;'
                            },
                            {field: "invcNo", title: "发票号",sort: true, align: "center", width: 188},
                            {field: "invoicingDate", title: "开票日期",sort: true, align: "center", width: 105},
                            {
                                field: "salesCompanyName",
                                title: "销货单位名称",
                                sort: true,
                                align: "center",
                                width: 218,
                                style: 'text-align:left;'
                            },
                            {
                                field: "amount",
                                title: "金额",
                                sort: true,
                                align: "center",
                                width: 100,
                                style: 'text-align:right;',
                                templet: function (d) {
                                    return formatNumContainNull(d.amount)
                                }
                            },
                            {
                                field: "taxPrice",
                                title: "税额",
                                sort: true,
                                align: "center",
                                width: 100,
                                style: 'text-align:right;',
                                templet: function (d) {
                                    return formatNumContainNull(d.taxPrice)
                                }
                            },
                            {
                                field: "totalTaxPrice",
                                title: "价税合计",
                                sort: true,
                                align: "center",
                                width: 100,
                                style: 'text-align:right;',
                                templet: function (d) {
                                    return formatNumContainNull(d.totalTaxPrice)
                                }
                            },
                            {field: "taxRate", title: "税率",sort: true, align: "center", width: 100},
                            {field: "openingDate", title: "开业日期",sort: true, align: "center", width: 105},
                            {field: "nationalStandardType", title: "国际行业大类",sort: true, align: "center", width: 120},
                            {field: "sameAddrInvTag", title: "注册地址",sort: true, align: "center", width: 80},
                            // {field: "", title: "招待费占收比", align: "center", width: 120},
                            {field: "isIssueInvTag", title: "是否成立<br>即开票",sort: true, align: "center", width: 80},
                            {field: "issueInvTag",  title: "成立即开票",sort: true, align: "center", width: 100},
                            {field: "isSameDayStoreInvTag", title: "是否同日同<br>店开票",sort: true, align: "center", width: 100},
                            {
                                field: "sameDayStoreInvTag",
                                sort: true,
                                title: "同日同店开票",
                                align: "center",
                                width: 120
                            },
                            {field: "isSameAddrInvTag", title: "是否不同店注<br>册地址相同",sort: true, align: "center", width: 120},
                            {
                                field: "sameAddrInvTag",
                                sort: true,
                                title: "不同店注册<br>地址相同",
                                align: "center",
                                width: 90
                            },
                            {field: "diffPlaceInvTag", title: "是否异地<br>开票",sort: true, align: "center", width: 80},
                            {field: "unreasonableInvTag", title: "是否事由存<br>在不合理",sort: true, align: "center", width: 100},
                            {field: "marketInvTag", title: "是否店名含贸<br>易零售等字样",sort: true, align: "center", width: 120},
                            {
                                field: "isSameStaffInvTag",
                                title: "是否相同报账人<br>同日多店开票",
                                sort: true,
                                align: "center",
                                width: 140
                            },
                            {
                                field: "sameStaffInvTag",
                                sort: true,
                                title: "相同报账人同<br>日多店开票",
                                align: "center",
                                width: 120
                            }
                        ]
                    ],
                    initSort: {
                        field: drillingFlag, // 默认排序字段
                        type: 'asc'          // 排序方式：升序
                    },
                    done: function (res, curr, count) {
                        page = res.page;
                        limit = res.limit;

                    },
                });
            };
            tableLoad(1)
            table.on('sort(table_1)', function (obj) {//此处的exhibition为你自己设置的table标签的lay-filter属性值

                // 保存滚动条位置
                var scrollPosition = $('.layui-table-body').scrollLeft();
                console.log('obj',obj)
                sortStates = {sortKey:obj.field,sortValue:obj.type=="asc"}
                // 尽管我们的 table 自带排序功能，但并没有请求服务端。
                // 有些时候，你可能需要根据当前排序的字段，重新向后端发送请求，从而实现服务端排序，如：
                table.reload('table_1', {
                    initSort: obj, // 记录初始排序，如果不设的话，将无法标记表头的排序状态。
                    scrollPos: 'keep',
                    where: { // 请求参数（注意：这里面的参数可任意定义，并非下面固定的格式。）
                     "sortList":[{"sortKey":obj.field,"sortValue":obj.type=="asc"}]
                    },done: function (res, curr, count) {
                        // 恢复滚动条位置
                        $('.layui-table-body').scrollLeft(scrollPosition);
                    }
                });
            });
            //页面宽度变化时触发
            $(window).resize(function () {
                table.reload('table_1');
            })

            //跳转钻取
            window.jumpPage = function (type, provCode, areaCode) {

                if(type=='provName'){
                    tagIndex = 2;
                    $('.layui-card-header-select').removeClass('active');
                    $('.layui-card-header-select[data-code="2"]').addClass('active');
                    $('#provCode').val(provCode)
                    $('#areaCode').val('')
                    getAuditSendArea()
                    tableLoad1()
                }else{
                    tagIndex = 3;
                    $('.layui-card-header-select').removeClass('active');
                    $('.layui-card-header-select[data-code="3"]').addClass('active');
                    drillingFlag = type;

                    $('#provCode').val(provCode)
                    $('#areaCode').val('')
                    if (areaCode) {
                        getAuditSendArea('drilling', areaCode)
                    } else {
                        getAuditSendArea()
                        tableLoad2()
                    }
                }

                form.render()
            };

            window.tableLoadFun = function () {
                drillingFlag = ''
                if (tagIndex == '1') {
                    tableLoad()
                } else if (tagIndex == '2') {
                    tableLoad1()
                } else if (tagIndex == '3') {
                    tableLoad2()
                }
            }

            window.restFun = function () {
                console.log(monthIdOld)
                $('#monthId').val(monthIdOld)
                $('#provCode').val('')
                $('#areaCode').val('')
                lp(tpl, [], selectTpl2.innerHTML, $('#areaCode'));
                form.render();
                tableLoadFun();
            };

        }
    );
</script>
