<!--嵌入式监控首页-->
<!--#include virtual ="include/header.html"-->
<link href="resource/css/audit/online/tagmodel/tagIndex.css?v=6.5" rel="stylesheet" type="text/css">
<link href="resource/css/audit/online/tagmodel/tagCommon.css?v=6.5" rel="stylesheet" type="text/css">
<style>
    .layui-common-card-shadow {
        padding: 10px;
        box-shadow: 0px 0px 12px 1px rgba(0, 0, 0, 0.1) !important;
    }

    .layui-common-body {
        background-color: #F4F4F4;
    }

    .layui-common-card {
        box-shadow: 0 0 0 0 #fff;
    }

    /*数据总览开始*/
    .data-screening .data-screening-l {
        width: 100%;
    }

    .data-screening .data-screening-r {
        width: 224px;
    }

    .data-screening-ul {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .data-screening-li {
        width: calc((100% / 6) - 1px);
        /*width: calc(100% / 6);*/
        height: 100px;
        display: inline-block;
        padding: 8px 20px 8px 0;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
    }

    .data-screening-box {
        height: 100%;
        padding: 16px 0 0 16px;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        background: #FFFFFF;
        box-shadow: 0 0 10px 1px rgba(0, 0, 0, 0.11);
        border-radius: 4px;
    }

    .data-screening-left {
        float: left;
    }

    .data-screening-icon {
        display: inline-block;
        width: 52px;
        height: 52px;
        line-height: 52px;
        text-align: center;
        border-radius: 4px;
    }

    .data-screening-icon1 {
        background: #F9F3EB;
    }

    .data-screening-icon1 .iconfont {
        color: #FB9338;
    }

    .data-screening-icon2 {
        background: #E7F8FB;
    }

    .data-screening-icon2 .iconfont {
        color: #14B1C4;
    }

    .data-screening-icon3 {
        background: #E8F8F0;
    }

    .data-screening-icon3 .iconfont {
        color: #31CC7F;
    }

    .data-screening-icon4 {
        background: #FCF1F3;
    }

    .data-screening-icon4 .iconfont {
        color: #F1313B;
    }

    .data-screening-icon5 {
        background: #EAF5FB;
    }

    .data-screening-icon5 .iconfont {
        color: #5BA7EB;
    }

    .data-screening-icon .iconfont {
        font-size: 28px;
    }

    .data-screening-right {
        padding-left: 14px;
        float: left;
        width: calc(100% - 67px);
    }

    .data-screening-right-title {
        font-size: 14px;
        font-weight: 400;
        color: #3F434A;
        width: 100%;
    }

    .data-screening-right-num {
        margin-top: 8px;
        font-size: 20px;
        font-weight: bold;
        color: #C20000;
        width: 100%;
    }

    .data-screening-r {
        text-align: center;
        padding-top: 3px;
    }

    .screening-r-btn {
        margin-left: 18px;
        margin-top: 10px;
        width: 185px;
        height: 30px;
        line-height: 30px;
        color: #C20000;
        background: #FCECED;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #C20000;
        justify-content: center;
    }

    .screening-r-btn .icon {
        margin-right: 14px;
    }

    /*数据总览结束*/

    /*嵌入式监控列表 开始*/
    .monitoring-list {
        width: 272px;
    }

    .monitoring-list .layui-common-card-content {
        height: 689px;
    }

    .monitoring-list-title {
        width: 100%;
        height: 41px;
        line-height: 41px;
        padding-left: 27px;
        font-size: 18px;
        font-weight: bold;
        color: #333333;
        box-sizing: border-box;
        background-color: #FFF3F3;
    }

    .monitoring-list-ul {
        width: 100%;
        height: calc(100% - 41px);
        overflow: auto;
    }

    .monitoring-list-li {
        position: relative;
        width: 100%;
        height: 44px;
        line-height: 44px;
        padding-left: 10px;
        box-sizing: border-box;
        border-bottom: 1px solid #fff;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #E4E4E4;
    }

    .monitoring-list-li img {
        margin-right: 8px;
    }

    .monitoring-list-li:before {
        content: '';
        width: 8px;
        height: 8px;
        background: #E4E4E4;
        border-radius: 10px;
        margin-right: 10px;
    }

    .monitoring-list-li-link:before {
        display: none;
    }

    .monitoring-list-li.active:before {
        background: #C20000;
    }

    .monitoring-list-li span {
        font-size: 14px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
    }

    .monitoring-list-li.active span {
        color: #C20000
    }

    .monitoring-list-li.active {
        background-color: #FFF0F0;
    }

    /*嵌入式监控列表 结束*/

    /*数据 echarts开始*/
    .monitoring-data {
        width: calc(100% - 272px);
    }

    .monitoring-data .layui-common-card-content {
        height: 689px;
    }

    .monitoring-data-title {
        width: 100%;
        height: 72px;
        line-height: 28px;
        padding: 0 13px;
        padding-top: 7px;
        box-sizing: border-box;
        background: #F6FCFF;
        border-radius: 4px;
        border: 1px solid #ABDCFF;
    }

    .monitoring-data-title span {
        font-size: 14px;
    }

    .monitoring-echart-box {
        width: 100%
    }

    .monitoring-echart-li {
        padding: 0 27px;
        box-sizing: border-box;
    }

    .monitoring-echart-li-header {
        height: 44px;
        line-height: 44px;
        width: 100%;
        font-size: 18px;
        font-weight: bold;
        color: #333333;
        border-bottom: 1px solid #E4E4E4;
    }

    .monitoring-echart-li-content {
        width: 100%;
        height: 204px;
    }
    .monitoring-echart-li-content1 {
        width: 100%;
        height: 260px;
    }
    /*数据 echarts结束*/
    /*风险提示 风险事项 开始*/
    .embedded-tab {
        margin: 0 10px;
        font-size: 16px;
        font-weight: bold;
        color: #333333;
        position: relative;
        display: inline-block;
    }

    .embedded-tab-active:before {
        content: '';
        position: absolute;
        left: 5%;
        bottom: 0;
        width: 90%;
        height: 3px;
        background: #C20000;
    }

    .layui-table thead tr {
        background-color: #f4f8fc;
        color: #333;
    }

    .common-btn {
        float: right;
        padding: 0px 15px;
        height: 30px;
        line-height: 30px;
        border: 1px solid red;
        border-radius: 4px;
        color: red;
        cursor: pointer;
    }

    .layui-table-page div {
        text-align: right;
    }

    .layui-table-body.layui-table-main {
        overflow-x: hidden;
    }
    .rightone {
        margin-top:4px;
        height: 150px;
        padding-right: 0 !important;
        background-image: url(resource/images/welcomefour1.png);
        background-size: 100% 100%;
        box-shadow: 0px 0px 10px 0px rgba(155, 11, 9, 0.1);
        border-radius: 4px;
        padding-top: 15px;
        box-sizing: border-box;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
    }
    /*风险提示 风险事项 结束*/

   .rightone .right-search-li{
       height: 40px;
        display: flex;
       align-items: center;
        justify-content: flex-start;
       width: 100%;
    }
    .rightone .right-search-li-title{
        width: 64px;
        height: 22px;
        margin:0 10px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #FFF4F4;
        line-height: 22px;
        text-align: right;
        font-style: normal;
    }
    .rightone .layui-form-radio *{
        color:#fff;
    }
    .right-search-li-input{
        width: calc(100% - 99px);
        align-items: center;
    }
    .right-search-li-input .iconfont{
        color:#fff;
        font-weight: 400;
        margin-right:10px;
    }
    .right-search-li-input-1{
        width: calc(100% - 45px);
        height: 32px;
        line-height: 32px;
        text-align: center;
        background: #EEEEEE;
        border-radius: 3px;
        font-weight: 400;
        font-size: 16px;
        color: #C20000;
    }
    .right-search-li-input-btn{
        width: auto;
        padding: 0 20px;
        display: inline-block;
        box-sizing: border-box;
        height: 32px;
        line-height: 32px;
        background: #EEEEEE;
        border-radius: 3px;
    }
    .right-search-li-input-btn .right-search-li-input-span{
        width: auto;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #F5222D;
        line-height: 32px;
        text-align: left;
        font-style: normal;
    }
    .right-search-li-input-btn .iconfont{
        color:#F5222D;
    }
    .patentNumber span{
        font-family: SourceHanSansCN, SourceHanSansCN;
        font-weight: bold;
        font-size: 18px;
        color: #333333;
        margin-left:12px;
        text-align: left;
        font-style: normal;
    }
    .patentNumber{
        display: flex;
        align-items: center;
        height: 100%;
        text-align: center;
        justify-content: end;
    }
    @media(max-width: 1680px) and (min-width: 1000px) {
        .rightone .right-search-li-title{
            font-size: 11px;
        }
        .data-screening-box{
            padding-left:8px;
            padding-top: 22px;
        }
        .data-screening-icon {
            width: 40px;
            height: 40px;
            line-height: 40px;
        }
        .data-screening-icon .iconfont {
            font-size: 17px;
        }
        .data-screening-right-title{
            font-size: 12px;
        }
        .data-screening-right {
            padding-left: 8px;
            width: calc(100% - 54px);
        }
        .data-screening-right-num {
            font-size: 16px;
        }
        .right-search-li-input {
            width: calc(100% - 74px);
        }
        .rightone .right-search-li-title {
            width: 50px;
        }
    }

</style>

<body>
<div class="layui-row layui-common-body">
    <div class="layui-row layui-col-md12 layui-col-sm12 layui-col-lg12">
        <!--数据总览-->
       <div class="layui-row">
           <div class="layui-col-md9 layui-col-sm9 layui-col-lg9 data-screening">
               <div class="layui-common-box">
                   <div class="layui-common-card">
                       <div class="layui-common-header">
                           <div class=" layui-common-card-header">
                               <div class="layui-card-header-title">监控总览</div>
                               <div class="patentNumber">
                                   <img src="resource/images/online/patentNumber.png" style="width: 51px;height: 32px" alt="">
                                    <span>专利号202311550992.0</span>
                               </div>
                           </div>
                       </div>
                       <div class="layui-common-card-content paddingLeft20Top10 layui-row" style="padding:0 0 4px 20px;">
                           <div class="data-screening-l float-left">
                               <ul class="data-screening-ul flex">
                                   <li class="data-screening-li">
                                       <div class="data-screening-box clear">
                                           <div class="data-screening-left">
                                               <div class="data-screening-icon data-screening-icon1">
                                                   <i class="iconfont icon">&#xe854;</i>
                                               </div>
                                           </div>
                                           <div class="data-screening-right">
                                               <p title="监控点-已上线" class="data-screening-right-title ovflowHidden">监控点-已上线
                                               </p>
                                               <p class="data-screening-right-num" id="onlinePointNum">0</p>
                                           </div>
                                       </div>
                                   </li>
                                   <li class="data-screening-li">
                                       <div class="data-screening-box clear">
                                           <div class="data-screening-left">
                                               <div class="data-screening-icon data-screening-icon5">
                                                   <i class="iconfont icon">&#xe7f0;</i>
                                               </div>
                                           </div>
                                           <div class="data-screening-right">
                                               <p title="监控点-未上线" class="data-screening-right-title ovflowHidden">
                                                   监控点-未上线</p>
                                               <p class="data-screening-right-num" id="offlinePointNum">0</p>
                                           </div>
                                       </div>
                                   </li>
                                   <li class="data-screening-li">
                                       <div class="data-screening-box clear cursor" onclick="jumpPageTop(0)">
                                           <div class="data-screening-left">
                                               <div class="data-screening-icon data-screening-icon2">
                                                   <i class="iconfont icon">&#xe7cb;</i>
                                               </div>
                                           </div>
                                           <div class="data-screening-right">
                                               <p title="当日风险提示总量" class="data-screening-right-title ovflowHidden">
                                                   风险提示总量</p>
                                               <p class="data-screening-right-num" id="tipsNum">0</p>
                                           </div>
                                       </div>
                                   </li>
                                   <li class="data-screening-li">
                                       <div class="data-screening-box clear cursor" onclick="jumpPageTop(1)">
                                           <div class="data-screening-left">
                                               <div class="data-screening-icon data-screening-icon3">
                                                   <i class="iconfont icon">&#xe856;</i>
                                               </div>
                                           </div>
                                           <div class="data-screening-right">
                                               <p title="当日风险事项总量" class="data-screening-right-title ovflowHidden">
                                                   风险事项总量</p>
                                               <p class="data-screening-right-num" id="pointNum">0</p>
                                           </div>
                                       </div>
                                   </li>
                                   <li class="data-screening-li">
                                       <div class="data-screening-box clear cursor" onclick="jumpPageTop(2)">
                                           <div class="data-screening-left">
                                               <div class="data-screening-icon data-screening-icon4">
                                                   <i class="iconfont icon">&#xe755;</i>
                                               </div>
                                           </div>
                                           <div class="data-screening-right">
                                               <p title="当日主动终止总量" class="data-screening-right-title ovflowHidden">
                                                   主动终止总量</p>
                                               <p class="data-screening-right-num" id="stopNum">0</p>
                                           </div>
                                       </div>
                                   </li>
                                   <li class="data-screening-li">
                                       <div class="data-screening-box clear cursor" onclick="jumpPageTop(3)">
                                           <div class="data-screening-left">
                                               <div class="data-screening-icon data-screening-icon5">
                                                   <i class="iconfont icon">&#xe672;</i>
                                               </div>
                                           </div>
                                           <div class="data-screening-right">
                                               <p title="风险总量" class="data-screening-right-title ovflowHidden">
                                                   异常业务量</p>
                                               <p class="data-screening-right-num" id="riskNum">0</p>
                                           </div>
                                       </div>
                                   </li>
                               </ul>
                           </div>
<!--                           <div class="data-screening-r float-right">-->
<!--                               <div class="screening-r-btn cursor flex"><i-->
<!--                                       class="iconfont icon">&#xe813;</i>-->
<!--                                   <span>监控进度维护</span>-->
<!--                                   &lt;!&ndash;onclick="monitoringProgressMaintenance()"&ndash;&gt;-->
<!--                               </div>-->
<!--                               <div class="screening-r-btn cursor flex" onclick="listUpload()"><i-->
<!--                                       class="iconfont icon">&#xe812;</i>-->
<!--                                   <span>监控清单上传</span>-->
<!--                               </div>-->
<!--                           </div>-->
                       </div>
                   </div>
               </div>
           </div>
           <div class="layui-col-lg3 layui-col-sm3" style="padding-right: 0;">
               <div class="rightone">
                   <div class="layui-form right-search-li">
                       <span class="right-search-li-title">快速选择</span>
                       <div class="right-search-li-input">
                           <input  lay-filter="yearAccumulation" name="yearAccumulation" title="当天" type="radio" value="2">
                           <input checked lay-filter="yearAccumulation" name="yearAccumulation" title="当年" type="radio" value="1">
                           <input  lay-filter="yearAccumulation" name="yearAccumulation" title="累计" type="radio" value="3">
                           <input lay-filter="yearAccumulation" name="yearAccumulation" title="自定义" type="radio" value="4">
                       </div>
                   </div>
                   <div class="layui-form right-search-li">
                       <span class="right-search-li-title">账期选择</span>
                       <div class="right-search-li-input flex">
                           <i class="iconfont">&#xe7ee;</i>
                           <input class="right-search-li-input-1 layui-input " style="display: none" readonly id="payment" value=""  type="text" >
                           <input class="right-search-li-input-1 layui-input " readonly id="paymentDisabled" value="-"  type="text" >
                       </div>
                   </div>
                   <div class="layui-form right-search-li" style="display: none" id="uploadBtn">
                       <span class="right-search-li-title">  </span>
                       <div class="right-search-li-input" style="margin-left:32px;">
                            <div class="right-search-li-input-btn cursor" onclick="listUpload()">
                                <i class="iconfont">&#xe858;</i>
                                <span class="right-search-li-input-span">监控清单上传</span>

                            </div>
                       </div>
                   </div>
               </div>
           </div>
       </div>
        <div class="monitoring-list float-left">
            <div class="layui-common-box">
                <div class="layui-common-card" style="padding: 12px 20px;box-sizing: border-box">
                    <div class="layui-common-card-content">
                        <div class="layui-common-card border-box layui-common-card-shadow" style="height:100%;">
                            <div class="monitoring-list-title cursor" onclick="monitoringAll()">嵌入式监控点</div>
                            <ul class="monitoring-list-ul" id="monitoringList">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="monitoring-data float-left">
            <div class="layui-common-box">
                <div class="layui-common-card" style="padding: 12px 20px;box-sizing: border-box">
                    <div class="layui-common-card-content">
                        <div class="monitoring-data-top layui-common-card border-box layui-common-card-shadow"
                             style="height:325px;">
                            <div class="monitoring-data-title ovflowHidden">
                                <b><span class="text-red">业务风险：</span></b>
                                <span id="riskDescription"></span>
                                <b><span class="text-red">监控对象：</span></b>
                                <span id="monitorObject"></span>
                                <b><span class="text-red">对接系统：</span></b>
                                <span id="systemName"></span>
                                <br>
                                当前累计签订合同<b><span class="text-red" id="signedContractNumber"></span></b>个，当前监控点可覆盖<b><span class="text-red" id="coveredContractNumber"></span></b>个
                            </div>
                            <div class="monitoring-echart-box layui-row" id="ordinaryCharts">
                                <div class="monitoring-echart-li  layui-col-md4 layui-col-sm4 layui-col-lg4">
                                    <div class="monitoring-echart-li-header">监控点统计</div>
                                    <div class="monitoring-echart-li-content" id="container1"></div>
                                </div>
                                <div class="monitoring-echart-li  layui-col-md4 layui-col-sm4 layui-col-lg4">
                                    <div class="monitoring-echart-li-header">风险提示TOP5</div>
                                    <div class="monitoring-echart-li-content" id="container2"></div>
                                </div>
                                <div class="monitoring-echart-li  layui-col-md4 layui-col-sm4 layui-col-lg4">
                                    <div class="monitoring-echart-li-header">企业高频提示</div>
                                    <div class="monitoring-echart-li-content" id="container3"></div>
                                </div>
                            </div>

                            <div class="monitoring-echart-box layui-row" id="supplyChaincharts" style="display: none">
                                <div class="monitoring-echart-li  layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="monitoring-echart-li-header">监控点统计</div>
                                    <div class="monitoring-echart-li-content monitoring-echart-li-content1" id="supplyChain1"></div>
                                </div>
                                <div class="monitoring-echart-li  layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="monitoring-echart-li-header">风险企业统计</div>
                                    <div class="monitoring-echart-li-content monitoring-echart-li-content1" id="supplyChain2"></div>
                                </div>
                                <div class="monitoring-echart-li  layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="monitoring-echart-li-header">审计风险企业统计</div>
                                    <div class="monitoring-echart-li-content monitoring-echart-li-content1" id="supplyChain3"></div>
                                </div>
                                <div class="monitoring-echart-li  layui-col-md3 layui-col-sm3 layui-col-lg3">
                                    <div class="monitoring-echart-li-header">企业查询次数统计</div>
                                    <div class="monitoring-echart-li-content monitoring-echart-li-content1" id="supplyChain4"></div>
                                </div>
                            </div>
                            <div class="monitoring-echart-box layui-row" id="vsensTerminal" style="display: none">
                                <div class="monitoring-echart-li  layui-col-md4 layui-col-sm4 layui-col-lg4">
                                    <div class="monitoring-echart-li-header">监控点统计</div>
                                    <div class="monitoring-echart-li-content" id="vsensTerminal1"></div>
                                </div>
                                <div class="monitoring-echart-li  layui-col-md4 layui-col-sm4 layui-col-lg4">
                                    <div class="monitoring-echart-li-header">供应商异常订单数量TOP5</div>
                                    <div class="monitoring-echart-li-content" id="vsensTerminal2"></div>
                                </div>
                                <div class="monitoring-echart-li  layui-col-md4 layui-col-sm4 layui-col-lg4">
                                    <div class="monitoring-echart-li-header">供应商异常串码统计TOP5</div>
                                    <div class="monitoring-echart-li-content" id="vsensTerminal3"></div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-common-card border-box layui-common-card-shadow" style="margin-top:16px;">
                            <div class="layui-common-header">
                                <div class=" layui-common-card-headers">
                                    <div class="layui-card-header-title" id="tabList">
                                        <span class="embedded-tab embedded-tab-active cursor" id="embeddedTab1" data-code="1">合同系统风险提示</span>
                                        <span class="embedded-tab cursor" id="embeddedTab2" data-code="2">合同系统风险事项</span>
                                        <span class="embedded-tab cursor" id="embeddedTab3" data-code="3">供应链系统监控提示</span>
                                        <span class="embedded-tab cursor" id="embeddedTab4" data-code="4">华盛终端串码采购监控</span>
                                    </div>
                                    <div class="common-btn" onclick="jumpPage()">监控明细</div>
                                </div>
                            </div>
                            <div class="layui-common-card-content table_data1"
                                 style="height: 280px;padding:6px 20px;box-sizing: border-box;display:none">
                                <div class="layui-table" id="table_data1" lay-filter="table_data1"></div>
                            </div>
                            <div class="layui-common-card-content table_data2"
                                 style="height: 280px;padding:6px 20px;box-sizing: border-box;display:none">
                                <div class="layui-table" id="table_data2" lay-filter="table_data1"></div>
                            </div>
                            <div class="layui-common-card-content table_data3"
                                 style="height: 280px;padding:6px 20px;box-sizing: border-box;display:none">
                                <div class="layui-table" id="table_data3" lay-filter="table_data1"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="tab-menu">
        <a data-parent="true" href="javascript:" id="tableMenu" style="color: #c00; text-decoration: underline">
            <i class="iconfont" data-icon=""></i>
        </a>
    </div>
</div>
<!--#include virtual ="include/version.html"-->
<script src="resource/js/echarts/echarts.min.js?v=6.5" type="text/javascript"></script>
<script src="resource/js/pagelog.js?v=6.5" type="text/javascript"></script>

<script id="monitoring-list-tpl" type="text/html">
    {{# layui.each(d, function(index, item){ }}
    {{#  if(item.status == '2') {}}
    <li class="monitoring-list-li   cursor  {{item.htmlUrl?'monitoring-list-li-link':''}} {{activeIndex==index?'active':''}}"
        data-status="{{item.status}}" data-code="{{item.monitoringPointCode}}" data-id="{{item.id}}"
        data-url="{{item.htmlUrl}}">
        {{# }else if(item.status == '1') { }}
    <li class="monitoring-list-li cursor {{activeIndex==index?'active':''}}" style="background-color: #FFFCEC;"
        data-status="{{item.status}}" data-code="{{item.monitoringPointCode}}" data-id="{{item.id}}"
        data-url="{{item.htmlUrl}}">
        {{# }else if(item.status == '0'){ }}
    <li class="monitoring-list-li cursor  {{activeIndex==index?'active':''}}"
        style="background-color: #EEEEEE;cursor: no-drop;pointer-events: none;" data-status="{{item.status}}"
        data-code="{{item.monitoringPointCode}}" data-id="{{item.id}}" data-url="{{item.htmlUrl}}">
        {{# }else{ }}
    <li class="monitoring-list-li cursor {{activeIndex==index?'active':''}}" data-status="{{item.status}}"
        data-code="{{item.monitoringPointCode}}" data-id="{{item.id}}" data-url="{{item.htmlUrl}}">
        {{# }}}
        {{# if(item.status == '0') {}}

        <span class="ovflowHidden" title="{{item.monitoringPointName}}">{{item.monitoringPointName}}</span>
        {{# }else{}}
        {{# if(item.htmlUrl) {}}
        <img src="resource/images/colligate/link.png">
        {{# }}}
        <span class="ovflowHidden" title="{{item.monitoringPointName}}" style="pointer-events: none;">{{item.monitoringPointName}}</span>
        {{# }}}
    </li>
    {{# }) }}
</script>
<script>
    $("title").text("嵌入式监控");
    if(loadCurUser().loginName && loadCurUserPost().provinceCode){
        console.log("new_monitor:嵌入式监控")
        isTianyanCollectData(loadCurUser().loginName,loadCurUserPost().provinceCode);
    }
    var layer, ctx, $, form, frm, risk_index = 1, activeIndex = 0;
    layui.use(['jqform', 'jqfrm', 'table', 'laytpl', 'jquery', 'laydate', 'layer', 'jqbind', 'upload', 'jqztree'], function () {
        layer = layui.layer;
        ctx = top.global.ctx;
        $ = layui.jquery;
        form = layui.jqform;
        frm = layui.jqfrm;
        var tpl = layui.laytpl,
            table = layui.table,
            laydate = layui.laydate,
            upload = layui.upload,
            jqbind = layui.jqbind;
        jqbind.init();
        var list = [];//左侧列表数据
        var monitoringPointCode = '';//监控点id

        var isAdmin = hasRole('LCCSRY'); //是否为admin系统管理员
        if(isAdmin){
            $('#uploadBtn').show()
        }else{
            $('#uploadBtn').hide()
        }
        /**----------------------------上侧 统计数据----------------------------*/
        window.loadTop = function () {
            var yearAccumulation = $('input:radio[name="yearAccumulation"]:checked').val();
            var value = $('#payment').val()
            var startTime = value.split(' - ')[0]
            var endTime = value.split(' - ')[1]
            $.ajax({
                url: ctx + "/online/embeddedRisk/queryStatisticSummary",
                dataType: "JSON",
                type: "POST",
                data: JSON.stringify({
                    type: yearAccumulation,
                    startTime:startTime,
                    endTime:endTime
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        var data = res.data;
                        $("#onlinePointNum").text(data.onlinePointNum);
                        $("#offlinePointNum").text(data.offlinePointNum);
                        $("#tipsNum").text(data.tipsNum);
                        $("#pointNum").text(data.pointNum);
                        $("#stopNum").text(data.stopNum);
                        $("#confirmNum").text(data.confirmNum);
                        $("#riskNum").text(data.riskNum);
                    }
                },
                error: function (e) {
                    console.info('加载统计数据出错:' + e.info);
                }
            });
        }
        /**----------------------------上侧 统计数据----------------------------*/

        /**----------------------------左侧嵌入式监控----------------------------*/
        //通过不同monitoringPointCode执行
        window.locationData = function(){
            if(monitoringPointCode=='HSZDCMCGJK'){//特殊处理
                $('#ordinaryCharts').hide()
                $('.monitoring-data-title').hide();
                $('#supplyChaincharts').hide()
                $('#vsensTerminal').show() //中间统计图
                queryVsensStatisticInfo();//加载图形
                risk_index = 4;
                $('.embedded-tab').removeClass('embedded-tab-active');
                $('[data-code="4"]').addClass('embedded-tab-active');
                //$('#tabList').hide()
                $('#embeddedTab1').hide()
                $('#embeddedTab2').hide()
                $('#embeddedTab3').hide()
                $('#embeddedTab4').show()

            }else if(monitoringPointCode=='GYLGFXQYJK'){//特殊处理
                $('#ordinaryCharts').hide()
                $('.monitoring-data-title').hide();
                $('#supplyChaincharts').show() //中间统计图
                $('#vsensTerminal').hide()
                querySupplyStatisticInfo();
                risk_index = 3;
                $('.embedded-tab').removeClass('embedded-tab-active');
                $('[data-code="3"]').addClass('embedded-tab-active');
                //$('#tabList').hide()
                $('#embeddedTab1').hide()
                $('#embeddedTab2').hide()
                $('#embeddedTab3').show()
                $('#embeddedTab4').hide()

            }else if(monitoringPointCode==''){
                $('#ordinaryCharts').show()
                $('.monitoring-data-title').hide();
                $('#supplyChaincharts').hide() //中间统计图
                $('#vsensTerminal').hide()
                loadCenterChart();
                risk_index = 1;
                $('.embedded-tab').removeClass('embedded-tab-active');
                $('[data-code="1"]').addClass('embedded-tab-active');
                //$('#tabList').show()
                $('#embeddedTab1').show()
                $('#embeddedTab2').show()
                $('#embeddedTab3').show()
                $('#embeddedTab4').show()
            }else{
                $('.monitoring-data-title').show();
                $('#ordinaryCharts').show()
                $('#supplyChaincharts').hide() //中间统计图
                $('#vsensTerminal').hide()
                loadCenterChart();
                risk_index = 1;
                $('.embedded-tab').removeClass('embedded-tab-active');
                $('[data-code="1"]').addClass('embedded-tab-active');
                //$('#tabList').show()
                $('#embeddedTab1').show()
                $('#embeddedTab2').show()
                $('#embeddedTab3').hide()
                $('#embeddedTab4').hide()
                //中间基本信息
                loadCenterBasic(list[activeIndex].id);
            }
            tableDataList = [];
            page = 1;
            tableData();
        }
        window.loadLeft = function () {
            $.ajax({
                url: ctx + "/online/embeddedRisk/queryEmbeddedPointList",
                dataType: "JSON",
                type: "POST",
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                         list = res.data;
                        lp(tpl, list, $("#monitoring-list-tpl").html(), $("#monitoringList"));
                        form.render();
                        if(list.length>0){
                          monitoringPointCode = list[activeIndex].monitoringPointCode;
                        //中间基本信息
                        loadCenterBasic(list[activeIndex].id);
                        locationData()
                      }
                    }
                },
                error: function (e) {
                    console.info('加载嵌入式监控列表出错:' + e.info);
                }
            });
        }

        //左侧：嵌入式监控列表切换
        $('#monitoringList').on('click', '.monitoring-list-li', function () {
            var _this = $(this);
            $('.monitoring-list-li').removeClass('active');
            _this.addClass('active');

            var id = _this.attr('data-id');
            var code = _this.attr('data-code');
            monitoringPointCode = code
            var url = _this.attr('data-url');
            if (url) {
                //url不为空时候不查看中间统计列表
                var title = _this.children("span").attr('title');
                closeTab(title);
                $("#tableMenu")
                    .data("title", title)
                    .data(
                        "url", url + "?monitoringPointCode=" + code
                    )
                    .click();
            } else {
                tableDataList = [];
                page = 1;
                if(monitoringPointCode!='GYLGFXQYJK'&&monitoringPointCode!=''){//特殊处理
                    //中间基本信息
                    loadCenterBasic(id);
                }
                locationData()

            }
        });
        //嵌入式监控点
        window.monitoringAll = function () {
            $('.monitoring-list-li').removeClass('active');
            $('.monitoring-data-title').hide();
            monitoringPointCode = ''
            tableDataList = [];
            page = 1;
            locationData();
        };
        /**----------------------------左侧嵌入式监控----------------------------*/

        /**----------------------------中间基本信息----------------------------*/
        window.loadCenterBasic = function (id) {
            var yearAccumulation = $('input:radio[name="yearAccumulation"]:checked').val();
            var value = $('#payment').val()
            var startTime = value.split(' - ')[0]
            var endTime = value.split(' - ')[1]
            $.ajax({
                url: ctx + "/online/embeddedRisk/queryEmbeddedBasicInfo",
                dataType: "JSON",
                type: "POST",
                data: JSON.stringify({
                    id: id,
                    type:yearAccumulation,
                    startTime:startTime,
                    endTime:endTime,
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        var data = res.data;
                        $('#riskDescription').text(data.riskDescription);
                        $('#monitorObject').text(data.monitorObject);
                        $('#systemName').text(data.systemName);
                        $('#signedContractNumber').text(data.signedContractNumber?data.signedContractNumber:0);
                        $('#coveredContractNumber').text(data.coveredContractNumber?data.coveredContractNumber:0);
                    }else{
                        // $('.monitoring-data-title').show();
                        $('#riskDescription').text('');
                        $('#monitorObject').text('');
                        $('#systemName').text('');
                        $('#signedContractNumber').text(0);
                        $('#coveredContractNumber').text(0);
                    }
                },
                error: function (e) {
                    console.info('加载嵌入式基本信息出错:' + e.info);
                }
            });
        }
        /**----------------------------中间基本信息----------------------------*/

        /**----------------------------中间统计图----------------------------*/
        window.loadCenterChart = function () {
            var yearAccumulation = $('input:radio[name="yearAccumulation"]:checked').val();

            var value = $('#payment').val()
            var startTime = value.split(' - ')[0]
            var endTime = value.split(' - ')[1]
            $.ajax({
                url: ctx + "/online/embeddedRisk/queryEmbeddedStatisticInfo",
                dataType: "JSON",
                type: "POST",
                data: JSON.stringify({
                    /**  监控点编码 */
                    monitoringPointCode: monitoringPointCode,
                    /**  系统编码 */
                    systemCode: "contract",
                    /** 历史累计；2实时 */
                    type: yearAccumulation,
                    startTime:startTime,
                    endTime:endTime
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        var monitoring = res.monitoring;
                        var tipTop5 = res.tipTop5;
                        var high = res.high;
                        initChart(document.getElementById("container1"), monitoring);
                        initChart1(document.getElementById("container2"), tipTop5,1);
                        initChart1(document.getElementById("container3"), high,2);
                    }
                },
                error: function (e) {
                    console.info('加载嵌入式基本信息出错:' + e.info);
                }
            });
        }


        //华盛终端chart
        window.vsensTerminal1Chart = function (element,data,color,type) {
            var myChart = echarts.init(element);
            var title = []; //x轴值
            var taskNumber = []; //任务
            for (var i = 0; i < data.length; i++) {
                title.push(data[i].name)
                taskNumber.push(data[i].num)
            }
            option = {
                color: [color],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    }
                },
                grid: {
                    left: '12px',
                    right: '0',
                    bottom: '14px',
                    containLabel: true
                },
                xAxis: [{
                    type: 'category',
                    data: title,
                    key: 'codeText',
                    axisPointer: {
                        type: 'shadow'
                    },
                    axisLabel: {
                        interval: 0,
                        rotate: type==1?25:0,
                        formatter: function (value) {
                            if (value.length > 6) {
                                return value.slice(0, 6)
                            }
                            return value
                        }
                    }
                }],
                yAxis: [{
                    type: 'value',
                    axisLabel: {
                        formatter: '{value}'
                    }
                }],
                series: [{
                    name: '数量',
                    type: 'bar',
                    barWidth: '18',
                    data: taskNumber,
                    label: {
                        normal: {
                            show: true,
                            position: 'top',
                            textStyle: {
                                color: '#000'
                            }
                        }
                    }
                }
                ]
            };
            myChart.setOption(option, true);
        };
        // 华盛终端
        window.queryVsensStatisticInfo = function () {
            var yearAccumulation = $('input:radio[name="yearAccumulation"]:checked').val();

            var value = $('#payment').val()
            var startTime = value.split(' - ')[0]
            var endTime = value.split(' - ')[1]
            $.ajax({
                url: ctx + "/online/embedded/vsensterminal/vsensTerminal1Chart1",
                dataType: "JSON",
                type: "POST",
                data: JSON.stringify({
                    /** 历史累计；2实时 */
                    type: yearAccumulation,
                    startTime:startTime,
                    endTime:endTime
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        vsensTerminal1Chart(document.getElementById("vsensTerminal1"),
                            [
                                {name:'订单量',num:res.data.allNum},
                                {name:'异常订单量',num:res.data.abnormalNum},
                                {name:'企业数',num:res.data.enterpriseNum}
                            ],'#51A0F3',0
                        );
                    }
                },
                error: function (e) {
                    console.info('加载嵌入式基本信息出错:' + e.info);
                }
            });
            $.ajax({
                url: ctx + "/online/embedded/vsensterminal/vsensTerminal1Chart2",
                dataType: "JSON",
                type: "POST",
                data: JSON.stringify({
                    /** 历史累计；2实时 */
                    type: yearAccumulation,
                    startTime:startTime,
                    endTime:endTime
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        vsensTerminal1Chart(document.getElementById("vsensTerminal2"),res.data.abnormalOrderData,'#65CA93',1);
                    }
                },
                error: function (e) {
                    console.info('加载嵌入式基本信息出错:' + e.info);
                }
            });
            $.ajax({
                url: ctx + "/online/embedded/vsensterminal/vsensTerminal1Chart3",
                dataType: "JSON",
                type: "POST",
                data: JSON.stringify({
                    /** 历史累计；2实时 */
                    type: yearAccumulation,
                    startTime:startTime,
                    endTime:endTime
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        vsensTerminal1Chart(document.getElementById("vsensTerminal3"),res.data.abnormalStringCodeData,'#65CA93',1);
                    }
                },
                error: function (e) {
                    console.info('加载嵌入式基本信息出错:' + e.info);
                }
            });
        }
        // 供应链支撑
        window.querySupplyStatisticInfo = function () {
            var yearAccumulation = $('input:radio[name="yearAccumulation"]:checked').val();

            var value = $('#payment').val()
            var startTime = value.split(' - ')[0]
            var endTime = value.split(' - ')[1]
            $.ajax({
                url: ctx + "/online/embeddedRiskSupply/querySupplyStatisticInfo",
                dataType: "JSON",
                type: "POST",
                data: JSON.stringify({
                    /**  监控点编码 */
                    monitoringPointCode: monitoringPointCode,
                    /**  系统编码 */
                    systemCode: "contract",
                    /** 历史累计；2实时 */
                    type: yearAccumulation,
                    startTime:startTime,
                    endTime:endTime
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        var monitoring = res.monitoring;
                        var tipTop5 = res.tipTop5;
                        var high = res.high;
                        supplyChainChart1(document.getElementById("supplyChain1"),
                            [
                                {codeText:'查询数',queryNum:res.riskSupplyReturns.queryNum},
                                {codeText:'查询企业',queryNum:res.riskSupplyReturns.queryEnterpriseNum}
                            ],'#51A0F3',0
                        );
                        supplyChainChart1(document.getElementById("supplyChain3"),res.riskSupplyReturns.auditRiskSupplyEnterpriseVos,'#65CA93',0);
                        supplyChainChart1(document.getElementById("supplyChain2"),res.riskSupplyReturns.riskSupplyEnterpriseVos,'#FD5353',0);
                        supplyChainChart1(document.getElementById("supplyChain4"),res.riskSupplyReturns.queryTimesVos,'#60cbff',1);
                    }
                },
                error: function (e) {
                    console.info('加载嵌入式基本信息出错:' + e.info);
                }
            });
        }
        window.initChart = function (element, data) {
            var myChart = echarts.init(element);
            // myChart.dispose();
            var title = []; //x轴值
            var taskNumber = []; //任务
            for (var i = 0; i < data.length; i++) {
                title.push(data[i].codeText)
                taskNumber.push(data[i].taskNumber)
            }
            option = {
                color: ['#FF5E5E'],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    }
                },
                grid: {
                    left: '12px',
                    right: '0',
                    bottom: '14px',
                    containLabel: true
                },
                xAxis: [{
                    type: 'category',
                    data: title,
                    key: 'codeText',
                    axisPointer: {
                        type: 'shadow'
                    },
                    axisLabel: {
                        interval: 0,
                        rotate: 25,
                        formatter: function (value) {
                            if (value.length > 6) {
                                return value.slice(0, 6)
                            }
                            return value
                        }
                    }
                }],
                yAxis: [{
                    type: 'value',
                    axisLabel: {
                        formatter: '{value}'
                    }
                }],
                series: [{
                    name: '数量',
                    type: 'bar',
                    barWidth: '18',
                    data: taskNumber,
                    label: {
                        normal: {
                            show: true,
                            position: 'top',
                            textStyle: {
                                color: '#000'
                            }
                        }
                    }
                }
                ]
            };
            myChart.setOption(option, true);
            myChart.off('click');// 清除事件
            myChart.on('click', function (params) {
                var yearAccumulation = $('input:radio[name="yearAccumulation"]:checked').val();

                var value = $('#payment').val()
                var startTime = value.split(' - ')[0]
                var endTime = value.split(' - ')[1]
                var riskTypes = '1'
                console.log(params.name=="风险提示量",params.name)
                if(yearAccumulation=='1'){
                if(params.name=="风险提示量"){
                        riskTypes='1'
                }else if(params.name=="风险事项量"){
                        riskTypes='2'
                }else{
                        riskTypes='3'
                }
                    closeTab(params.name);
                    $("#tableMenu")
                        .data("title", params.name)
                        .data(
                            "url",
                            "/views/audit/online/embedded/tab.html?getType=1&riskType=" + riskTypes+'&monitoringPointCode='+monitoringPointCode+'&startTime='+startTime+'&endTime='+endTime
                        )
                        .click();
                }
            })
        };
        window.initChart1 = function (element, data,type) {
            var myChart = echarts.init(element);
            // myChart.dispose();
            var title = []; //x轴值
            var stopNum = []; //任务
            var pointNum = []; //任务
            var tipsNum = []; //任务
            for (var i = 0; i < data.length; i++) {
                title.push(data[i].codeText)
                pointNum.push(data[i].pointNum)
                stopNum.push(data[i].stopNum)
                tipsNum.push(data[i].tipsNum)
            }
            console.log(stopNum,pointNum,tipsNum)
            option = {
                color: ['#FF5E5E','#4692ff'],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    }
                },
                formatter: function(param){
                    var index = param[0].dataIndex;
                    // param是悬浮窗所在的数据（x、y轴数据）
                    let relVal = ""
                    // param[0]可以获取x y轴的数据
                    relVal += param[0].name + "<br/>" + "风险提示合同数量" + ":"+ data[index].tipsNum+
                        "<br/>" + "风险事项合同数量" + ":"+ data[index].pointNum+
                        "<br/>" + "主动终止合同数量" + ":"+ data[index].stopNum
                    return relVal
                   },
                grid: {
                    left: '12px',
                    right: '0',
                    bottom: '14px',
                    containLabel: true
                },
                xAxis: [{
                    type: 'category',
                    data: title,
                    key: 'codeText',
                    axisPointer: {
                        type: 'shadow'
                    },
                    axisLabel: {
                        interval: 0,
                        rotate: 25,
                        formatter: function (value) {
                            if (value.length > 6) {
                                return value.slice(0, 6)
                            }
                            return value
                        }
                    }
                }],
                yAxis: [{
                    type: 'value',
                    axisLabel: {
                        formatter: '{value}'
                    }
                }],
                series: [{
                    name: '风险事项合同数量',
                    type: 'bar',
                    stack: 'Ad',
                    barWidth: '18',
                    data: pointNum,
                    label: {
                        normal: {
                            show: false,
                            position: 'top',
                            textStyle: {
                                color: '#000'
                            }
                        }
                    }
                },
                    {
                        name: '主动终止合同数量',
                        type: 'bar',
                        stack: 'Ad',
                        emphasis: {
                            focus: 'series'
                        },
                        data: stopNum,
                        itemStyle: {
                            normal: {
                                label: {
                                    show: true, //开启显示
                                    position: 'top', //在上方显示
                                    formatter:function(val){
                                        console.log(val.dataIndex)
                                        var dataIndex = val.dataIndex;
                                        return tipsNum[dataIndex]
                                    },
                                    textStyle: { //数值样式
                                        color: 'black',
                                        fontSize: 12
                                    }
                                }
                            }
                        }
                    },
                ]
            };
            myChart.setOption(option, true);
            myChart.off('click');// 清除事件
            myChart.on('click', function (params) {
                var index = params.dataIndex;
                var dataJson = data[index];
                if(params.seriesIndex==0){
                    dataJson.riskType='2'
                }else{
                    dataJson.riskType='3'
                }
                jumpPageEchart(type,dataJson)
            })
        };
        window.jumpPageEchart = function(type,data){
            console.log(data);
            var yearAccumulation = $('input:radio[name="yearAccumulation"]:checked').val();
            var value = $('#payment').val()
            var startTime = value.split(' - ')[0]
            var endTime = value.split(' - ')[1]
            if(yearAccumulation=='1'){
                if(type==1){//风险提示TOP5
                    if(data.code!=''){
                        if(data.code[0]!='0'){
                            data.code='00'+data.code;
                        }
                    }
                    closeTab(data.codeText);
                    $("#tableMenu")
                        .data("title", data.codeText)
                        .data(
                            "url",
                            "/views/audit/online/embedded/tab.html?getType=1&riskType=" + data.riskType+'&provCode='+data.code+'&provName='+encodeURI(data.codeText)+'&monitoringPointCode='+monitoringPointCode+'&startTime='+startTime+'&endTime='+endTime
                        )
                        .click();
                }else{//企业高频提示
                    closeTab(data.codeText);
                    $("#tableMenu")
                        .data("title", data.codeText)
                        .data(
                            "url",
                            "/views/audit/online/embedded/tab.html?getType=1&riskType=" + data.riskType+'&contractSecendp='+encodeURI(data.codeText)+'&monitoringPointCode='+monitoringPointCode+'&startTime='+startTime+'&endTime='+endTime
                        )
                        .click();
                }
            }


        };

        //特殊chart 供应链
        window.supplyChainChart1 = function (element,data,color,type) {
            var myChart = echarts.init(element);
            var title = []; //x轴值
            var taskNumber = []; //任务
            for (var i = 0; i < data.length; i++) {
                title.push(data[i].codeText)
                taskNumber.push(data[i].queryNum)
            }
            option = {
                color: [color],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    }
                },
                grid: {
                    left: '12px',
                    right: '0',
                    bottom: '14px',
                    containLabel: true
                },
                xAxis: [{
                    type: 'category',
                    data: title,
                    key: 'codeText',
                    axisPointer: {
                        type: 'shadow'
                    },
                    axisLabel: {
                        interval: 0,
                        rotate: type==1?25:0,
                        formatter: function (value) {
                            if (value.length > 6) {
                                return value.slice(0, 6)
                            }
                            return value
                        }
                    }
                }],
                yAxis: [{
                    type: 'value',
                    axisLabel: {
                        formatter: '{value}'
                    }
                }],
                series: [{
                    name: '数量',
                    type: 'bar',
                    barWidth: '18',
                    data: taskNumber,
                    label: {
                        normal: {
                            show: true,
                            position: 'top',
                            textStyle: {
                                color: '#000'
                            }
                        }
                    }
                }
                ]
            };
            myChart.setOption(option, true);
        };
        /**----------------------------中间统计图----------------------------*/

        /**----------------------------下面：风险提示 风险事项----------------------------*/
        var tableDataList = [];
        var page = 1;
        var scrollTop = 0;
        //风险提示 风险事项 切换
        $('.embedded-tab').click(function () {
            risk_index = $(this).data('code');
            var _this = $(this);
            $('.embedded-tab').removeClass('embedded-tab-active');
            _this.addClass('embedded-tab-active');
            tableDataList = [];
            page = 1;
            //风险提示、风险事项列表查询
            tableData();
        });
        //风险提醒
        window.tableData = function () {
            var yearAccumulation = $('input:radio[name="yearAccumulation"]:checked').val();
            var value = $('#payment').val()
            scrollTop = 0;
            var startTime = value.split(' - ')[0]
            var endTime = value.split(' - ')[1]
            var url = '/online/embeddedRisk/queryRiskMatterList'
            if(risk_index=='3'){
                url = '/online/embeddedSupplyDetailSummary/querySupplySummaryList'
            }else if(risk_index=='4'){
                url = '/online/embedded/vsensterminal/queryList'
            }else{

            }
            if(risk_index=='3'){
                $('.table_data2').show()
                $('.table_data1').hide()
                $('.table_data3').hide()
                querySupplySummaryList()
            }else if(risk_index=='4'){
                $('.table_data3').show()
                $('.table_data1').hide()
                $('.table_data2').hide()
                queryList();
            }else{
                $('.table_data1').show()
                $('.table_data2').hide()
                $('.table_data3').hide()
                queryRiskMatterList();
            }

        };
        // 风险提醒1 2
        window.queryRiskMatterList = function(){
            var yearAccumulation = $('input:radio[name="yearAccumulation"]:checked').val();
            var value = $('#payment').val()
            scrollTop = 0;
            var startTime = value.split(' - ')[0]
            var endTime = value.split(' - ')[1]
            $.ajax({
                url: ctx + '/online/embeddedRisk/queryRiskMatterList',
                dataType: "JSON",
                type: "POST",
                data: JSON.stringify({
                    riskType: risk_index,
                    limit: 10,
                    page: page,
                    type:yearAccumulation,
                    monitoringPointCode: monitoringPointCode,
                    startTime:startTime,
                    endTime:endTime
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        page++;
                        tableDataList = res.data;
                        var cols;
                        switch (risk_index) {
                            case 1:
                                //风险提示
                                cols = [[
                                    {field: 'serialNumber', title: '合同流水', align: 'center', width: '18%'},
                                    {field: 'systemName', title: '触发系统', align: 'center', width: '17%'},
                                    {field: 'riskTime', title: '触发时间', align: 'center', width: '17%'},
                                    {
                                        field: 'triggerEventContent',
                                        title: '触发内容',
                                        align: 'center',
                                        style: 'text-align: left;',
                                        width: '47%',
                                        templet: function (d) {
                                            return '<span class="ovflowHidden" title="'+d.triggerEventContent+'">'+d.triggerEventContent+'</span>';
                                        },
                                        event: 'clicks'
                                    },
                                ]];
                                break;
                            case 2:
                                //风险事项
                                cols = [[
                                    {field: 'serialNumber', title: '合同流水', align: 'center', width: '15%'},
                                    {field: 'systemName', title: '系统', align: 'center', width: '12%'},
                                    {field: 'riskTime', title: '回传时间', align: 'center', width: '17%'},
                                    {field: 'monitoringPointName', title: '风险点', align: 'center', width: '15%'},
                                    {
                                        field: 'triggerEventContent',
                                        title: '风险描述',
                                        align: 'center',
                                        style: 'text-align: left;',
                                        width: '40%',
                                        templet: function (d) {
                                            return '<span class="ovflowHidden" title="'+d.triggerEventContent+'">'+d.triggerEventContent+'</span>';
                                        },
                                        event: 'clicks'
                                    },
                                ]];
                                break;
                        }
                        table.render({
                            elem:"#table_data1",
                            height: "268",
                            data: tableDataList,
                            limit: 99999,
                            page: false,
                            cols: cols,
                            done: function (res, curr, count) {
                                    var i=0;
                                    $(".table_data1 .layui-table-body.layui-table-main").scrollTop(scrollTop)
                                    //scroll_view是总容器的id
                                    $('.table_data1 .layui-table-body.layui-table-main').scroll(function () {
                                        scrollTop = $(".table_data1 .layui-table-body.layui-table-main").scrollTop();
                                        var height = $(".table_data1 .layui-table-body.layui-table-main").height();
                                        var scrollHeight = $(".table_data1 .layui-table-body.layui-table-main")[0].scrollHeight;
                                        if (scrollTop + height >= scrollHeight-2) {
                                            i++;
                                            if(i===1){
                                                queryRiskMatterListRender();
                                            }
                                            $('.table_data1 .layui-table-body.layui-table-main').css('overflow-y', 'hidden')
                                            setTimeout(function () {
                                                $('.table_data1 .layui-table-body.layui-table-main').css('overflow-y', 'auto')
                                            }, 1000)
                                        }
                                    })
                            },
                        });
                    }
                },
                error: function (e) {
                }
            });
        };
        // 风险提醒3
        window.querySupplySummaryList = function(){
            var yearAccumulation = $('input:radio[name="yearAccumulation"]:checked').val();
            var value = $('#payment').val()
            scrollTop = 0;
            var startTime = value.split(' - ')[0]
            var endTime = value.split(' - ')[1]
            $.ajax({
                url: ctx + '/online/embeddedSupplyDetailSummary/querySupplySummaryList',
                dataType: "JSON",
                type: "POST",
                data: JSON.stringify({
                    riskType: risk_index,
                    limit: 10,
                    page: page,
                    type:yearAccumulation,
                    monitoringPointCode: monitoringPointCode,
                    startTime:startTime,
                    endTime:endTime
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        page++;
                        tableDataList = res.data;
                        var cols;
                         cols = [[
                             {field: 'triggerSystemText', title: '触发系统', align: 'center', width: '25%'},
                             {field: 'triggerTime', title: '触发时间', align: 'center', width: '25%'},
                             {
                                 field: 'triggerContent',
                                 title: '触发内容',
                                 align: 'center',
                                 style: 'text-align: left;',
                                 width: '50%',
                                 templet: function (d) {
                                     return '<span class="ovflowHidden" title="'+d.triggerContent+'">'+d.triggerContent+'</span>';
                                 }
                             },
                         ]];
                        table.render({
                            elem:"#table_data2",
                            height: "268",
                            data: tableDataList,
                            limit: 99999,
                            page: false,
                            cols: cols,
                            done: function (res, curr, count) {
                                var i=0;
                                $(".table_data2 .layui-table-body.layui-table-main").scrollTop(scrollTop)
                                //scroll_view是总容器的id
                                $('.table_data2 .layui-table-body.layui-table-main').scroll(function () {
                                    scrollTop = $(".table_data2 .layui-table-body.layui-table-main").scrollTop();
                                    var height = $(".table_data2 .layui-table-body.layui-table-main").height();
                                    var scrollHeight = $(".table_data2 .layui-table-body.layui-table-main")[0].scrollHeight;
                                    if (scrollTop + height >= scrollHeight-2) {
                                        i++;
                                        if(i===1){
                                            querySupplySummaryListRender();
                                        }
                                        $('.table_data2 .layui-table-body.layui-table-main').css('overflow-y', 'hidden')
                                        setTimeout(function () {
                                            $('.table_data2 .layui-table-body.layui-table-main').css('overflow-y', 'auto')
                                        }, 1000)
                                    }
                                })
                            },
                        });
                    }
                },
                error: function (e) {
                }
            });
        };
        // 风险提醒4
        window.queryList = function(){
            var yearAccumulation = $('input:radio[name="yearAccumulation"]:checked').val();
            var value = $('#payment').val()
            scrollTop = 0;
            var startTime = value.split(' - ')[0]
            var endTime = value.split(' - ')[1]
            $.ajax({
                url: ctx + '/online/embedded/vsensterminal/queryList',
                dataType: "JSON",
                type: "POST",
                data: JSON.stringify({
                    riskType: risk_index,
                    limit: 10,
                    page: page,
                    type:yearAccumulation,
                    monitoringPointCode: monitoringPointCode,
                    startTime:startTime,
                    endTime:endTime
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        page++;
                        tableDataList = res.data;
                        var cols;
                        cols = [[
                            {field: 'systemText', title: '触发系统', align: 'center', width: '25%',
                                templet: function (d) {
                                    return '联通华盛';
                                }},
                            {field: 'orderDate', title: '触发时间', align: 'center', width: '25%'},
                            {
                                field: 'triggerContent',
                                title: '触发内容',
                                align: 'center',
                                style: 'text-align: left;',
                                width: '50%',
                                templet: function (d) {
                                    return '<span class="ovflowHidden" title="'+d.triggerContent+'">'+d.triggerContent+'</span>';
                                }
                            },
                        ]];
                        table.render({
                            elem:"#table_data3",
                            height: "268",
                            data: tableDataList,
                            limit: 99999,
                            page: false,
                            cols: cols,
                            done: function (res, curr, count) {
                                var i=0;
                                $(".table_data3 .layui-table-body.layui-table-main").scrollTop(scrollTop)

                                //scroll_view是总容器的id
                                $('.table_data3 .layui-table-body.layui-table-main').scroll(function () {
                                    scrollTop = $(".table_data3 .layui-table-body.layui-table-main").scrollTop();
                                    var height = $(".table_data3 .layui-table-body.layui-table-main").height();
                                    var scrollHeight = $(".table_data3 .layui-table-body.layui-table-main")[0].scrollHeight;
                                    if (scrollTop + height >= scrollHeight-2) {
                                        i++;
                                        if(i===1){
                                            queryListRender();
                                        }
                                        $('.table_data3 .layui-table-body.layui-table-main').css('overflow-y', 'hidden')
                                        setTimeout(function () {
                                            $('.table_data3 .layui-table-body.layui-table-main').css('overflow-y', 'auto')
                                        }, 1000)
                                    }
                                })
                            },
                        });
                    }
                },
                error: function (e) {
                }
            });
        };

        // 风险提醒1 2
        window.queryRiskMatterListRender = function(){
            var yearAccumulation = $('input:radio[name="yearAccumulation"]:checked').val();
            var value = $('#payment').val()
            scrollTop = 0;
            var startTime = value.split(' - ')[0]
            var endTime = value.split(' - ')[1]
            $.ajax({
                url: ctx + '/online/embeddedRisk/queryRiskMatterList',
                dataType: "JSON",
                type: "POST",
                data: JSON.stringify({
                    riskType: risk_index,
                    limit: 10,
                    page: page,
                    type:yearAccumulation,
                    monitoringPointCode: monitoringPointCode,
                    startTime:startTime,
                    endTime:endTime
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        if (!res.data.length) {
                            return false;
                        }else{
                            page++;
                            tableDataList = tableDataList.concat(res.data);
                        }
                        var cols;
                        switch (risk_index) {
                            case 1:
                                //风险提示
                                cols = [[
                                    {field: 'serialNumber', title: '合同流水', align: 'center', width: '18%'},
                                    {field: 'systemName', title: '触发系统', align: 'center', width: '17%'},
                                    {field: 'riskTime', title: '触发时间', align: 'center', width: '17%'},
                                    {
                                        field: 'triggerEventContent',
                                        title: '触发内容',
                                        align: 'center',
                                        style: 'text-align: left;',
                                        width: '47%',
                                        templet: function (d) {
                                            return '<span class="ovflowHidden" title="'+d.triggerEventContent+'">'+d.triggerEventContent+'</span>';
                                        },
                                        event: 'clicks'
                                    },
                                ]];
                                break;
                            case 2:
                                //风险事项
                                cols = [[
                                    {field: 'serialNumber', title: '合同流水', align: 'center', width: '15%'},
                                    {field: 'systemName', title: '系统', align: 'center', width: '12%'},
                                    {field: 'riskTime', title: '回传时间', align: 'center', width: '17%'},
                                    {field: 'monitoringPointName', title: '风险点', align: 'center', width: '15%'},
                                    {
                                        field: 'triggerEventContent',
                                        title: '风险描述',
                                        align: 'center',
                                        style: 'text-align: left;',
                                        width: '40%',
                                        templet: function (d) {
                                            return '<span class="ovflowHidden" title="'+d.triggerEventContent+'">'+d.triggerEventContent+'</span>';
                                        },
                                        event: 'clicks'
                                    },
                                ]];
                                break;
                        }
                        table.render({
                            elem:"#table_data1",
                            height: "268",
                            data: tableDataList,
                            limit: 99999,
                            page: false,
                            cols: cols,
                            done: function (res, curr, count) {
                                var i=0;
                                $(".table_data1 .layui-table-body.layui-table-main").scrollTop(scrollTop)
                                //scroll_view是总容器的id
                                $('.table_data1 .layui-table-body.layui-table-main').scroll(function () {
                                    scrollTop = $(".table_data1 .layui-table-body.layui-table-main").scrollTop();
                                    var height = $(".table_data1 .layui-table-body.layui-table-main").height();
                                    var scrollHeight = $(".table_data1 .layui-table-body.layui-table-main")[0].scrollHeight;
                                    if (scrollTop + height >= scrollHeight-2) {
                                        i++;
                                        if(i===1){
                                            queryRiskMatterListRender();
                                        }
                                        $('.table_data1 .layui-table-body.layui-table-main').css('overflow-y', 'hidden')
                                        setTimeout(function () {
                                            $('.table_data1 .layui-table-body.layui-table-main').css('overflow-y', 'auto')
                                        }, 1000)
                                    }
                                })
                            },
                        });
                    }
                },
                error: function (e) {
                }
            });
        };
        // 风险提醒3
        window.querySupplySummaryListRender = function(){
            var yearAccumulation = $('input:radio[name="yearAccumulation"]:checked').val();
            var value = $('#payment').val()
            scrollTop = 0;
            var startTime = value.split(' - ')[0]
            var endTime = value.split(' - ')[1]
            $.ajax({
                url: ctx + '/online/embeddedSupplyDetailSummary/querySupplySummaryList',
                dataType: "JSON",
                type: "POST",
                data: JSON.stringify({
                    riskType: risk_index,
                    limit: 10,
                    page: page,
                    type:yearAccumulation,
                    monitoringPointCode: monitoringPointCode,
                    startTime:startTime,
                    endTime:endTime
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        if (!res.data.length) {
                            return false;
                        }else{
                            page++;
                            tableDataList = tableDataList.concat(res.data);
                        }
                        var cols;
                        cols = [[
                            {field: 'triggerSystemText', title: '触发系统', align: 'center', width: '25%'},
                            {field: 'triggerTime', title: '触发时间', align: 'center', width: '25%'},
                            {
                                field: 'triggerContent',
                                title: '触发内容',
                                align: 'center',
                                style: 'text-align: left;',
                                width: '50%',
                                templet: function (d) {
                                    return '<span class="ovflowHidden" title="'+d.triggerContent+'">'+d.triggerContent+'</span>';
                                }
                            },
                        ]];
                        table.render({
                            elem:"#table_data2",
                            height: "268",
                            data: tableDataList,
                            limit: 99999,
                            page: false,
                            cols: cols,
                            done: function (res, curr, count) {
                                var i=0;
                                $(".table_data2 .layui-table-body.layui-table-main").scrollTop(scrollTop)
                                //scroll_view是总容器的id
                                $('.table_data2 .layui-table-body.layui-table-main').scroll(function () {
                                    scrollTop = $(".table_data2 .layui-table-body.layui-table-main").scrollTop();
                                    var height = $(".table_data2 .layui-table-body.layui-table-main").height();
                                    var scrollHeight = $(".table_data2 .layui-table-body.layui-table-main")[0].scrollHeight;
                                    if (scrollTop + height >= scrollHeight-2) {
                                        i++;
                                        if(i===1){
                                            querySupplySummaryListRender();
                                        }
                                        $('.table_data2 .layui-table-body.layui-table-main').css('overflow-y', 'hidden')
                                        setTimeout(function () {
                                            $('.table_data2 .layui-table-body.layui-table-main').css('overflow-y', 'auto')
                                        }, 1000)
                                    }
                                })
                            },
                        });
                    }
                },
                error: function (e) {
                }
            });
        };
        // 风险提醒4
        window.queryListRender = function(){
            var yearAccumulation = $('input:radio[name="yearAccumulation"]:checked').val();
            var value = $('#payment').val()
            scrollTop = 0;
            var startTime = value.split(' - ')[0]
            var endTime = value.split(' - ')[1]
            $.ajax({
                url: ctx + '/online/embedded/vsensterminal/queryList',
                dataType: "JSON",
                type: "POST",
                data: JSON.stringify({
                    riskType: risk_index,
                    limit: 10,
                    page: page,
                    type:yearAccumulation,
                    monitoringPointCode: monitoringPointCode,
                    startTime:startTime,
                    endTime:endTime
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (res) {
                    if (res.httpCode == 200) {
                        if (!res.data.length) {
                            return false;
                        }else{
                            page++;
                            tableDataList = tableDataList.concat(res.data);
                        }
                        var cols;
                        cols = [[
                            {field: 'systemText', title: '触发系统', align: 'center', width: '25%',
                                templet: function (d) {
                                    return '联通华盛';
                                }},
                            {field: 'orderDate', title: '触发时间', align: 'center', width: '25%'},
                            {
                                field: 'triggerContent',
                                title: '触发内容',
                                align: 'center',
                                style: 'text-align: left;',
                                width: '50%',
                                templet: function (d) {
                                    return '<span class="ovflowHidden" title="'+d.triggerContent+'">'+d.triggerContent+'</span>';
                                }
                            },
                        ]];
                        table.render({
                            elem:"#table_data3",
                            height: "268",
                            data: tableDataList,
                            limit: 99999,
                            page: false,
                            cols: cols,
                            done: function (res, curr, count) {
                                var i=0;
                                $(".table_data3 .layui-table-body.layui-table-main").scrollTop(scrollTop)

                                //scroll_view是总容器的id
                                $('.table_data3 .layui-table-body.layui-table-main').scroll(function () {
                                    scrollTop = $(".table_data3 .layui-table-body.layui-table-main").scrollTop();
                                    var height = $(".table_data3 .layui-table-body.layui-table-main").height();
                                    var scrollHeight = $(".table_data3 .layui-table-body.layui-table-main")[0].scrollHeight;
                                    if (scrollTop + height >= scrollHeight-2) {
                                        i++;
                                        if(i===1){
                                            queryListRender();
                                        }
                                        $('.table_data3 .layui-table-body.layui-table-main').css('overflow-y', 'hidden')
                                        setTimeout(function () {
                                            $('.table_data3 .layui-table-body.layui-table-main').css('overflow-y', 'auto')
                                        }, 1000)
                                    }
                                })
                            },
                        });
                    }
                },
                error: function (e) {
                }
            });
        };



        table.on('tool(table_data)', function (obj) {
            var data = obj.data;
            //打开详情
            if (obj.event === 'clicks') {
                top.layer.open({
                    type: 2,
                    content: 'views/audit/online/embedded/detailsInfo.html?serialNumber=' + data.serialNumber,
                    title: '风险详情',
                    area: ['70%', '45%'],
                    //关闭刷新页面
                    end: function () {
                    }
                });
            }
        });
        /**----------------------------下面：风险提示 风险事项----------------------------*/

        //监控清单上传
        window.listUpload = function () {
            layer.open({
                type: 2,
                area: ['1200px', '90%'],
                content: 'views/audit/online/embedded/listUpload.html',
                title: '监控清单上传',
                success: function (layero, index) {
                },
                cancel: function (index) {
                },
                end: function () {
                }
            });
        }

        // 监控进度维护
        window.monitoringProgressMaintenance = function () {
            layer.open({
                type: 2,
                area: ['60%', '60%'],
                content: 'views/audit/online/embedded/monitoringProgressMaintenance.html',
                title: '监控进度维护',
                btn: ["确认", "取消"],
                yes: function (index, layero) {
                    var list = $(layero).find("iframe")[0].contentWindow.submit_1();
                },
                cancel: function (index) {
                },
                end: function () {
                }
            });
        }

        // 监控总览 跳转
        window.jumpPageTop = function (type) {
            var title = '';
            var riskType = '';//风险类别
            var systemCode = '';//来源系统
            var statisticStatus = '';//1：提示类 0：非提示类
            var yearAccumulation = $('input:radio[name="yearAccumulation"]:checked').val();
            var value = $('#payment').val()
            var startTime = value.split(' - ')[0]
            var endTime = value.split(' - ')[1]
            if (type == 0) {//风险提示总量
                title = '风险提示总量';
                systemCode = '';
                riskType = '1';
                statisticStatus = '1'
            } else if (type == 1) {//	风险事项总量
                title = '风险事项总量';
                systemCode = '';
                riskType = '2';
                statisticStatus = '1'
            } else if (type == 2) {//主动终止总量
                title = '主动终止总量';
                systemCode = '';
                riskType = '3';
                statisticStatus = '1'
            } else if (type == 3) {//	异常业务量
                title = '异常业务量';
                systemCode = 'contract';
                riskType = '2';
                statisticStatus = '0'
            }

            closeTab(title);
            $("#tableMenu")
                .data("title", title)
                .data(
                    "url",
                    "/views/audit/online/embedded/tab.html" +
                    "?getType=1&monitoringPointCode=" +
                    "&riskType=" + riskType +
                    "&systemCode=" + systemCode +
                    "&statisticStatus=" + statisticStatus+
                    "&yearAccumulation="+yearAccumulation+
                    "&startTime="+startTime+
                    "&endTime="+endTime
                )
                .click();
        }

        // 监控明细 按钮查询
        window.jumpPage = function () {
            if(risk_index == 4){
                closeTab("监控明细");
                $("#tableMenu")
                    .data("title", "监控明细")
                    .data(
                        "url",
                        "/views/audit/online/embedded/tab.html?getType=3"
                    )
                    .click();
            }else if(risk_index == 3){
                closeTab("监控明细");
                $("#tableMenu")
                    .data("title", "监控明细")
                    .data(
                        "url",
                        "/views/audit/online/embedded/tab.html?getType=2"
                    )
                    .click();
            }else{
                closeTab("监控明细");
                $("#tableMenu")
                    .data("title", "监控明细")
                    .data(
                        "url",
                        "/views/audit/online/embedded/tab.html?getType=1&riskType=" + risk_index
                    )
                    .click();
            }

        }
        //初始化账期
        var myDate = new Date();

        var tYear = myDate.getFullYear(); //当前年
        var tMonth = myDate.getMonth(); //当前月
        var tDay = myDate.getDate(); //当前日
        tMonth = tMonth + 1;
        if (tMonth.toString().length == 1) {
            tMonth = "0" + tMonth;
        }
        if (tDay.toString().length == 1) {
            tDay = "0" + tDay;
        }


        var startTimeS = tYear + '01'; //自定义开始时间
        var endTimeS = tYear + '' + tMonth; //自定义结束时间
        laydate.render({
            elem: '#payment', //指定元素
            type: 'month',
            range: true,
            format: 'yyyyMM',
            value:startTimeS+ ' - ' +endTimeS,
            done: function (value, date) {
                $('#payment').val(value)
                tableDataList = [];
                page = 1;
                loadTop();
                locationDataQuery();
            }
        });
        //默认当年
        $('#payment').hide()
        $('#paymentDisabled').show()
        $('#paymentDisabled').val(tYear+ ' - ' +tYear)
        //查询条件
        window.locationDataQuery = function(){
            if(monitoringPointCode=='HSZDCMCGJK'){
                queryVsensStatisticInfo();//加载图形
            }else if(monitoringPointCode=='GYLGFXQYJK'){//特殊处理
                querySupplyStatisticInfo();
            }else if(monitoringPointCode==''){
                loadCenterChart();
            }else{
                loadCenterChart();
            }
            tableDataList = [];
            page = 1;
            tableData();
        }
        form.on('radio(yearAccumulation)', function (data) {
            var dateType = $('input:radio[name="yearAccumulation"]:checked').val()
            if(dateType=='4'){//自定义
                $('#payment').val(startTimeS+ ' - ' +endTimeS)
                $('#payment').show()
                $('#paymentDisabled').hide()

            }else if(dateType=='1'){//今年
                $('#payment').hide()
                $('#paymentDisabled').show()
                $('#paymentDisabled').val(tYear+ ' - ' +tYear)
            }else if(dateType=='2'){//今天
                $('#payment').hide()
                $('#paymentDisabled').show()
                $('#paymentDisabled').val(endTimeS+''+tDay+ ' - ' +endTimeS+''+tDay)
            }else if(dateType=='3'){//累计
                $('#payment').hide()
                $('#paymentDisabled').show()
                $('#paymentDisabled').val('-')
            }
            loadTop();
            locationDataQuery();
            // locationData();
        });

        loadTop();
        loadLeft();

    });
</script>
</body>
